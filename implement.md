# Socket.IO Command Permission Validation Implementation

## Overview

This document outlines the step-by-step implementation plan for preventing users from sending Socket.IO commands they don't have permissions to execute. The implementation leverages the new permission system defined in CAT-491.sql to provide granular, role-based access control for device commands.

## Why This Change is Needed

### Current State
- **Basic Permission System**: The current system only validates device access at a high level (`org_view_devices`, `device_group_view_devices`, `location_group_view_devices`)
- **Command-Level Gaps**: Users can send any device command if they have basic device access, regardless of the specific operation
- **Security Risk**: A user with basic device viewing permissions could potentially send configuration changes, firmware updates, or system resets

### Target State
- **Granular Control**: Each device command type requires specific permissions based on the user's role
- **Role-Based Access**: Different user roles (Traffic Engineers, Signal Technicians, IT, etc.) have different command capabilities
- **Security Enhancement**: Prevents unauthorized device modifications and ensures compliance with organizational policies

### Business Impact
- **Compliance**: Ensures only authorized personnel can perform sensitive operations
- **Risk Mitigation**: Prevents accidental or malicious device configuration changes
- **Audit Trail**: Clear permission-based logging for all device operations
- **Operational Safety**: Protects critical infrastructure from unauthorized modifications

## Implementation Architecture

### Permission Mapping Strategy

Based on CAT-491.sql, we'll map device commands to permission groups:

```
Command Category          → Permission Group → Required Permissions
────────────────────────────────────────────────────────────────────
Configuration Read       → Group 3          → org_ng_read_config, device_group_ng_read_config, location_group_ng_read_config
Configuration Write      → Group 6          → org_ng_write_datakey, device_group_ng_write_datakey, location_group_ng_write_datakey
User Settings           → Group 7          → org_ng_write_user_settings, device_group_ng_write_user_settings, location_group_ng_write_user_settings
Statistics Read         → Group 1          → org_ng_read_stats, device_group_ng_read_stats, location_group_ng_read_stats
Statistics Reset        → Group 5          → org_ng_reset_stats, device_group_ng_reset_stats, location_group_ng_reset_stats
Log Access              → Group 0          → org_ng_all_access, device_group_ng_all_access, location_group_ng_all_access
Log Clearing            → Group 4          → org_ng_clear_logs, device_group_ng_clear_logs, location_group_ng_clear_logs
Time Settings           → Group 2          → org_ng_set_time, device_group_ng_set_time, location_group_ng_set_time
Firmware Updates        → Group 8          → org_ng_dfu, device_group_ng_dfu, location_group_ng_dfu
```

## Step-by-Step Implementation Plan

### Phase 1: Core Permission Infrastructure (Week 1)

#### 1.1 Create Command Permission Mapper
**File**: `microservices/rushhour/permissions/command_mapper.go`

```go
// CommandPermissionMapper maps device commands to required permissions
type CommandPermissionMapper struct {
    // Maps command types to permission requirements
    commandPermissions map[string][]string
}

// NewCommandPermissionMapper creates a new command permission mapper
func NewCommandPermissionMapper() *CommandPermissionMapper {
    mapper := &CommandPermissionMapper{
        commandPermissions: make(map[string][]string),
    }
    mapper.initializeCommandPermissions()
    return mapper
}

// initializeCommandPermissions sets up the command-to-permission mappings
func (cpm *CommandPermissionMapper) initializeCommandPermissions() {
    // Configuration Read Commands
    cpm.commandPermissions["rd_monitor_data"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_network_config_unit"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_network_config_active"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_channel_config"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_channel_current_sense"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_channel_permissives"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_fya_config"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_data_key"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_factory_settings"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_user_settings"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_port1_disables"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}
    cpm.commandPermissions["rd_date_time_dst"] = []string{"org_ng_read_config", "device_group_ng_read_config", "location_group_ng_read_config"}

    // Configuration Write Commands
    cpm.commandPermissions["wr_data_key"] = []string{"org_ng_write_datakey", "device_group_ng_write_datakey", "location_group_ng_write_datakey"}
    cpm.commandPermissions["wr_factory_settings"] = []string{"org_ng_write_datakey", "device_group_ng_write_datakey", "location_group_ng_write_datakey"}
    cpm.commandPermissions["wr_user_settings"] = []string{"org_ng_write_user_settings", "device_group_ng_write_user_settings", "location_group_ng_write_user_settings"}
    cpm.commandPermissions["wr_port1_disables"] = []string{"org_ng_write_datakey", "device_group_ng_write_datakey", "location_group_ng_write_datakey"}
    cpm.commandPermissions["wr_agency_options"] = []string{"org_ng_write_datakey", "device_group_ng_write_datakey", "location_group_ng_write_datakey"}

    // Statistics Commands
    cpm.commandPermissions["rd_port1_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats", "location_group_ng_read_stats"}
    cpm.commandPermissions["rd_data_key_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats", "location_group_ng_read_stats"}
    cpm.commandPermissions["rd_main_iso_comms_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats", "location_group_ng_read_stats"}
    cpm.commandPermissions["rd_main_comms_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats", "location_group_ng_read_stats"}
    cpm.commandPermissions["rd_comms_main_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats", "location_group_ng_read_stats"}
    cpm.commandPermissions["rd_flash_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats", "location_group_ng_read_stats"}
    cpm.commandPermissions["rd_watchdog_stats"] = []string{"org_ng_read_stats", "device_group_ng_read_stats", "location_group_ng_read_stats"}
    cpm.commandPermissions["clear_stats"] = []string{"org_ng_reset_stats", "device_group_ng_reset_stats", "location_group_ng_reset_stats"}

    // Log Commands
    cpm.commandPermissions["request_log_counts"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}
    cpm.commandPermissions["request_log"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}
    cpm.commandPermissions["req_auditlog_counts"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}
    cpm.commandPermissions["request_auditlog"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}
    cpm.commandPermissions["log_clear"] = []string{"org_ng_clear_logs", "device_group_ng_clear_logs", "location_group_ng_clear_logs"}
    cpm.commandPermissions["auditlog_clear"] = []string{"org_ng_clear_logs", "device_group_ng_clear_logs", "location_group_ng_clear_logs"}
    cpm.commandPermissions["audit_log_reset"] = []string{"org_ng_clear_logs", "device_group_ng_clear_logs", "location_group_ng_clear_logs"}

    // Time and System Commands
    cpm.commandPermissions["wr_date_time_dst"] = []string{"org_ng_set_time", "device_group_ng_set_time", "location_group_ng_set_time"}
    cpm.commandPermissions["remote_reset"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}
    cpm.commandPermissions["remote_display_button_event"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}

    // Realtime Data Commands
    cpm.commandPermissions["start_realtime"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}
    cpm.commandPermissions["stop_realtime"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}

    // DFU Commands
    cpm.commandPermissions["manifest_versions"] = []string{"org_ng_dfu", "device_group_ng_dfu", "location_group_ng_dfu"}
    cpm.commandPermissions["reboot_comms_mcu"] = []string{"org_ng_dfu", "device_group_ng_dfu", "location_group_ng_dfu"}
    cpm.commandPermissions["initiate_dfu"] = []string{"org_ng_dfu", "device_group_ng_dfu", "location_group_ng_dfu"}
    cpm.commandPermissions["send_fw_manifest"] = []string{"org_ng_dfu", "device_group_ng_dfu", "location_group_ng_dfu"}
    cpm.commandPermissions["begin_firmware_download"] = []string{"org_ng_dfu", "device_group_ng_dfu", "location_group_ng_dfu"}
    cpm.commandPermissions["download_firmware_chunk"] = []string{"org_ng_dfu", "device_group_ng_dfu", "location_group_ng_dfu"}

    // Test Commands
    cpm.commandPermissions["test_chunk"] = []string{"org_ng_all_access", "device_group_ng_all_access", "location_group_ng_all_access"}
}

// GetRequiredPermissions returns the required permissions for a specific command
func (cpm *CommandPermissionMapper) GetRequiredPermissions(commandType string) ([]string, bool) {
    permissions, exists := cpm.commandPermissions[commandType]
    return permissions, exists
}

// ValidateCommandPermissions checks if a user has the required permissions for a command
func (cpm *CommandPermissionMapper) ValidateCommandPermissions(
    userPermissions *authorizer.UserPermissions,
    deviceID string,
    commandType string,
    pg connect.DatabaseExecutor,
) error {
    requiredPermissions, exists := cpm.GetRequiredPermissions(commandType)
    if !exists {
        // Unknown command type - deny by default
        return fmt.Errorf("unknown command type: %s", commandType)
    }

    // Check if user has any of the required permissions for this device
    canAccess, err := userPermissions.CanAccessDevice(pg, deviceID, requiredPermissions...)
    if err != nil {
        return fmt.Errorf("failed to check command permissions: %w", err)
    }

    if !canAccess {
        return fmt.Errorf("insufficient permissions for command %s on device %s", commandType, deviceID)
    }

    return nil
}
```

#### 1.2 Update Permission Checker Interface
**File**: `microservices/rushhour/permissions/checker.go`

```go
// Add new method to PermissionChecker
func (pc *PermissionChecker) CheckCommandPermissions(
    ctx *domain.ConnectionContext,
    deviceID string,
    commandType string,
    pg connect.DatabaseExecutor,
) error {
    if ctx == nil {
        return fmt.Errorf("no connection context")
    }

    // Get user permissions from context
    if ctx.AuthInfo == nil || ctx.AuthInfo.Permissions == nil {
        return fmt.Errorf("no user permissions available")
    }

    // Use command permission mapper to validate
    mapper := NewCommandPermissionMapper()
    return mapper.ValidateCommandPermissions(ctx.AuthInfo.Permissions, deviceID, commandType, pg)
}
```

### Phase 2: Binary Message Handler Integration (Week 2)

#### 2.1 Implement Command Validation in Binary Handler
**File**: `microservices/rushhour/modules/socketio/binary_handler.go`

```go
// Update validateDeviceCommand method
func (bmh *BinaryMessageHandler) validateDeviceCommand(
    envelope *domain.SocketEnvelope,
    userPermissions *authorizer.UserPermissions,
    pg connect.DatabaseExecutor,
) error {
    if len(envelope.Payload) == 0 {
        return fmt.Errorf("command payload cannot be empty")
    }

    // Deserialize WrapperCommand from monf-protobufs-messages
    var wrapperCmd wrappers.WrapperCommand
    if err := proto.Unmarshal(envelope.Payload, &wrapperCmd); err != nil {
        return fmt.Errorf("failed to unmarshal wrapper command: %w", err)
    }

    // Extract command type and validate permissions
    commandType := bmh.extractCommandType(&wrapperCmd)
    if commandType == "" {
        return fmt.Errorf("unable to determine command type")
    }

    // Create command permission mapper
    mapper := NewCommandPermissionMapper()
    
    // Validate command permissions
    if err := mapper.ValidateCommandPermissions(
        userPermissions,
        envelope.DeviceId,
        commandType,
        pg,
    ); err != nil {
        return fmt.Errorf("command permission validation failed: %w", err)
    }

    logger.Debugf("Device command validation passed for device %s, command %s", 
        envelope.DeviceId, commandType)
    return nil
}

// extractCommandType determines the command type from WrapperCommand
func (bmh *BinaryMessageHandler) extractCommandType(wrapperCmd *wrappers.WrapperCommand) string {
    switch wrapperCmd.GetCommand().(type) {
    case *wrappers.WrapperCommand_RequestLogCounts:
        return "request_log_counts"
    case *wrappers.WrapperCommand_LogClear:
        return "log_clear"
    case *wrappers.WrapperCommand_RequestLog:
        return "request_log"
    case *wrappers.WrapperCommand_ReqAuditlogCounts:
        return "req_auditlog_counts"
    case *wrappers.WrapperCommand_AuditlogClear:
        return "auditlog_clear"
    case *wrappers.WrapperCommand_AuditLogReset:
        return "audit_log_reset"
    case *wrappers.WrapperCommand_RequestAuditlog:
        return "request_auditlog"
    case *wrappers.WrapperCommand_RdMonitorData:
        return "rd_monitor_data"
    case *wrappers.WrapperCommand_RdNetworkConfigUnit:
        return "rd_network_config_unit"
    case *wrappers.WrapperCommand_RdNetworkConfigActive:
        return "rd_network_config_active"
    case *wrappers.WrapperCommand_RdChannelConfig:
        return "rd_channel_config"
    case *wrappers.WrapperCommand_RdChannelCurrentSense:
        return "rd_channel_current_sense"
    case *wrappers.WrapperCommand_RdChannelPermissives:
        return "rd_channel_permissives"
    case *wrappers.WrapperCommand_RdFyaConfig:
        return "rd_fya_config"
    case *wrappers.WrapperCommand_RdDataKey:
        return "rd_data_key"
    case *wrappers.WrapperCommand_WrDataKey:
        return "wr_data_key"
    case *wrappers.WrapperCommand_RdFactorySettings:
        return "rd_factory_settings"
    case *wrappers.WrapperCommand_WrFactorySettings:
        return "wr_factory_settings"
    case *wrappers.WrapperCommand_RdUserSettings:
        return "rd_user_settings"
    case *wrappers.WrapperCommand_WrUserSettings:
        return "wr_user_settings"
    case *wrappers.WrapperCommand_RdPort1Disables:
        return "rd_port1_disables"
    case *wrappers.WrapperCommand_WrPort1Disables:
        return "wr_port1_disables"
    case *wrappers.WrapperCommand_WrAgencyOptions:
        return "wr_agency_options"
    case *wrappers.WrapperCommand_RemoteReset:
        return "remote_reset"
    case *wrappers.WrapperCommand_RdPort1Stats:
        return "rd_port1_stats"
    case *wrappers.WrapperCommand_RdDataKeyStats:
        return "rd_data_key_stats"
    case *wrappers.WrapperCommand_RdMainIsoCommsStats:
        return "rd_main_iso_comms_stats"
    case *wrappers.WrapperCommand_RdMainCommsStats:
        return "rd_main_comms_stats"
    case *wrappers.WrapperCommand_RdCommsMainStats:
        return "rd_comms_main_stats"
    case *wrappers.WrapperCommand_RdFlashStats:
        return "rd_flash_stats"
    case *wrappers.WrapperCommand_RdWatchdogStats:
        return "rd_watchdog_stats"
    case *wrappers.WrapperCommand_RdDateTimeDst:
        return "rd_date_time_dst"
    case *wrappers.WrapperCommand_ClearStats:
        return "clear_stats"
    case *wrappers.WrapperCommand_WrDateTimeDst:
        return "wr_date_time_dst"
    case *wrappers.WrapperCommand_RemoteDisplayButtonEvent:
        return "remote_display_button_event"
    case *wrappers.WrapperCommand_StartRealtime:
        return "start_realtime"
    case *wrappers.WrapperCommand_StopRealtime:
        return "stop_realtime"
    case *wrappers.WrapperCommand_ManifestVersions:
        return "manifest_versions"
    case *wrappers.WrapperCommand_RebootCommsMcu:
        return "reboot_comms_mcu"
    case *wrappers.WrapperCommand_InitiateDfu:
        return "initiate_dfu"
    case *wrappers.WrapperCommand_SendFwManifest:
        return "send_fw_manifest"
    case *wrappers.WrapperCommand_BeginFirmwareDownload:
        return "begin_firmware_download"
    case *wrappers.WrapperCommand_DownloadFirmwareChunk:
        return "download_firmware_chunk"
    case *wrappers.WrapperCommand_TestChunk:
        return "test_chunk"
    default:
        return ""
    }
}
```

#### 2.2 Update ProcessBinaryMessage Method
**File**: `microservices/rushhour/modules/socketio/binary_handler.go`

```go
// Update ProcessBinaryMessage to include database connection
func (bmh *BinaryMessageHandler) ProcessBinaryMessage(
    data []byte,
    userPermissions *authorizer.UserPermissions,
    orgID string,
    pg connect.DatabaseExecutor,
) (*domain.SocketEnvelope, error) {
    // Parse the rushhour envelope (outer layer)
    var envelope rushhourv1.SocketEnvelope
    if err := proto.Unmarshal(data, &envelope); err != nil {
        return nil, fmt.Errorf("failed to unmarshal rushhour envelope: %w", err)
    }

    logger.Debugf("Parsed rushhour envelope: type=%v, origin=%v, device=%s, org=%s",
        envelope.Type, envelope.Origin, envelope.DeviceId, envelope.OrganizationId)

    // FILTERING POINT 1: Validate envelope-level permissions
    if err := bmh.validateEnvelopePermissions(&envelope, userPermissions, orgID); err != nil {
        logger.Warnf("Envelope permission check failed: %v", err)
        return nil, fmt.Errorf("access denied: %w", err)
    }

    // For commands from FSA, inspect and validate the inner device envelope
    if envelope.Type == domain.EnvelopeTypeWrapperCommand && envelope.Origin == domain.OriginTypeFSA {
        if err := bmh.validateDeviceCommand(&envelope, userPermissions, pg); err != nil {
            logger.Warnf("Device command validation failed: %v", err)
            return nil, fmt.Errorf("command not permitted: %w", err)
        }
    }

    // For responses from gateways, optionally filter response data based on permissions
    if envelope.Type == domain.EnvelopeTypeWrapperResponse && envelope.Origin == domain.OriginTypeGateway {
        if err := bmh.validateDeviceResponse(&envelope, userPermissions); err != nil {
            logger.Warnf("Device response validation failed: %v", err)
            return nil, fmt.Errorf("response data filtered: %w", err)
        }
    }

    return &envelope, nil
}
```

### Phase 3: Service Layer Integration (Week 3)

#### 3.1 Update Service ProcessDeviceRequest Method
**File**: `microservices/rushhour/modules/socketio/service.go`

```go
// Update ProcessDeviceRequest to include command permission validation
func (s *Service) ProcessDeviceRequest(conn *socket.Socket, envelope *domain.SocketEnvelope) error {
    ctx, exists := s.socketRegistry.GetContext(string(conn.Id()))
    if !exists || ctx.AuthInfo == nil ||
        (ctx.AuthInfo.ClientType != domain.ClientTypeFSA && ctx.AuthInfo.ClientType != domain.ClientTypeOnramp) {
        return fmt.Errorf("unauthorized: not an FSA or Onramp connection")
    }

    // Validate device access using existing permission system
    if err := s.permissionChecker.CheckDeviceAccess(ctx, envelope.DeviceId, "control"); err != nil {
        return fmt.Errorf("access denied to device %s: %v", envelope.DeviceId, err)
    }

    // NEW: Validate command-specific permissions
    if err := s.permissionChecker.CheckCommandPermissions(
        ctx,
        envelope.DeviceId,
        envelope.Type.String(), // This will be enhanced to extract actual command type
        s.pg, // Database connection for permission validation
    ); err != nil {
        logger.Warnf("Command permission check failed for device %s: %v", envelope.DeviceId, err)
        return fmt.Errorf("command not permitted: %w", err)
    }

    // RH FILLS IN MISSING INFO: Enhance envelope with user/session/routing information
    enrichedEnvelope := proto.Clone(envelope).(*domain.SocketEnvelope)
    enrichedEnvelope.Origin = domain.OriginTypeFSA
    if ctx.AuthInfo.ClientType == domain.ClientTypeOnramp {
        enrichedEnvelope.Origin = domain.OriginTypeOnramp
    }

    // Fill in Session ID and User ID for proper routing and tracking
    enrichedEnvelope.SessionId = string(conn.Id())
    enrichedEnvelope.UserId = ctx.AuthInfo.UserID
    enrichedEnvelope.OrganizationId = ctx.AuthInfo.OrgID

    // Find the gateway responsible for this device
    gatewaySocketID, err := s.GetGatewaySocketForDevice(envelope.DeviceId)
    if err != nil {
        return fmt.Errorf("failed to find gateway for device %s: %w", envelope.DeviceId, err)
    }

    // Send directly to the specific gateway socket
    gatewayNamespace := s.server.Of(domain.NamespaceGateway, nil)
    gatewayNamespace.To(socket.Room(gatewaySocketID)).Emit("device_request", &enrichedEnvelope)

    return nil
}
```

#### 3.2 Add Database Connection to Service
**File**: `microservices/rushhour/modules/socketio/service.go`

```go
// Add database connection to Service struct
type Service struct {
    server            *socketio.Server
    socketRegistry    *SocketRegistry
    permissionChecker *permissions.PermissionChecker
    streamTracker     *StreamTracker
    pg                connect.DatabaseExecutor // NEW: Database connection for permission validation
}

// Update NewService constructor
func NewService(
    server *socketio.Server,
    socketRegistry *SocketRegistry,
    permissionChecker *permissions.PermissionChecker,
    streamTracker *StreamTracker,
    pg connect.DatabaseExecutor, // NEW: Database connection parameter
) *Service {
    return &Service{
        server:            server,
        socketRegistry:    socketRegistry,
        permissionChecker: permissionChecker,
        streamTracker:     streamTracker,
        pg:                pg, // NEW: Store database connection
    }
}
```

### Phase 4: Handler Layer Updates (Week 4)

#### 4.1 Update Handler to Pass Database Connection
**File**: `microservices/rushhour/modules/socketio/handler.go`

```go
// Update Handler struct to include database connection
type Handler struct {
    service *Service
    pg      connect.DatabaseExecutor // NEW: Database connection for permission validation
}

// Update NewHandler constructor
func NewHandler(service *Service, pg connect.DatabaseExecutor) *Handler {
    return &Handler{
        service: service,
        pg:      pg,
    }
}

// Update device_request handler to include permission validation
conn.On("device_request", func(datas ...interface{}) {
    if len(datas) == 0 {
        return
    }

    envelope, err := h.parseSocketEnvelope(datas[0])
    if err != nil {
        logger.Warnf("Invalid device request from FSA %s: %v", conn.Id(), err)
        return
    }

    // Process the device request (includes permission validation)
    if err := h.service.ProcessDeviceRequest(conn, envelope); err != nil {
        logger.Warnf("Device request processing failed: %v", err)
        conn.Emit("error", err.Error())
        return
    }

    conn.Emit("device_request_sent", map[string]interface{}{
        "device_id": envelope.DeviceId,
        "status":    "sent",
    })
})
```

### Phase 5: Main Application Integration (Week 5)

#### 5.1 Update Main Application to Provide Database Connection
**File**: `microservices/rushhour/main.go`

```go
// Update main function to provide database connection to handlers
func main() {
    // ... existing setup code ...

    // Initialize database connection
    pg, err := connect.NewPostgresConnection(ctx)
    if err != nil {
        logger.Fatalf("Failed to connect to database: %v", err)
    }
    defer pg.Close()

    // Initialize services with database connection
    permissionChecker := permissions.NewPermissionChecker()
    socketRegistry := socketio.NewSocketRegistry()
    streamTracker := socketio.NewStreamTracker()
    
    server := socketio.NewServer(nil)
    
    service := socketio.NewService(
        server,
        socketRegistry,
        permissionChecker,
        streamTracker,
        pg, // Pass database connection
    )
    
    handler := socketio.NewHandler(service, pg) // Pass database connection

    // ... rest of setup code ...
}
```

### Phase 6: Testing and Validation (Week 6)

#### 6.1 Unit Tests for Command Permission Mapper
**File**: `microservices/rushhour/permissions/command_mapper_test.go`

```go
func TestCommandPermissionMapper_ValidateCommandPermissions(t *testing.T) {
    tests := []struct {
        name              string
        commandType       string
        userPermissions   *authorizer.UserPermissions
        deviceID          string
        expectError       bool
        errorContains     string
    }{
        {
            name:        "valid read config permissions",
            commandType: "rd_monitor_data",
            userPermissions: &authorizer.UserPermissions{
                UserID: "user123",
                Permissions: []authorizer.Permission{
                    {
                        Scope:          "organization",
                        ScopeID:        "org456",
                        OrganizationID: "org456",
                        Permissions:    []string{"org_ng_read_config"},
                    },
                },
            },
            deviceID:      "device123",
            expectError:   false,
        },
        {
            name:        "insufficient permissions for write command",
            commandType: "wr_data_key",
            userPermissions: &authorizer.UserPermissions{
                UserID: "user123",
                Permissions: []authorizer.Permission{
                    {
                        Scope:          "organization",
                        ScopeID:        "org456",
                        OrganizationID: "org456",
                        Permissions:    []string{"org_ng_read_config"}, // Missing write permissions
                    },
                },
            },
            deviceID:      "device123",
            expectError:   true,
            errorContains: "insufficient permissions",
        },
        // Add more test cases for different command types and permission scenarios
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            mapper := NewCommandPermissionMapper()
            
            // Mock database connection for testing
            mockPG := &MockDatabaseExecutor{}
            
            err := mapper.ValidateCommandPermissions(
                tt.userPermissions,
                tt.deviceID,
                tt.commandType,
                mockPG,
            )

            if tt.expectError {
                assert.Error(t, err)
                if tt.errorContains != "" {
                    assert.Contains(t, err.Error(), tt.errorContains)
                }
            } else {
                assert.NoError(t, err)
            }
        })
    }
}
```

#### 6.2 Integration Tests
**File**: `microservices/rushhour/modules/socketio/binary_handler_integration_test.go`

```go
func TestBinaryMessageHandler_CommandPermissionIntegration(t *testing.T) {
    // Test end-to-end command permission validation
    // This would test the full flow from binary message to permission validation
}
```

### Phase 7: Monitoring and Logging (Week 7)

#### 7.1 Enhanced Logging for Permission Checks
**File**: `microservices/rushhour/permissions/command_mapper.go`

```go
// Add detailed logging to permission validation
func (cpm *CommandPermissionMapper) ValidateCommandPermissions(
    userPermissions *authorizer.UserPermissions,
    deviceID string,
    commandType string,
    pg connect.DatabaseExecutor,
) error {
    logger.Debugf("Validating command permissions: user=%s, device=%s, command=%s",
        userPermissions.UserID, deviceID, commandType)

    requiredPermissions, exists := cpm.GetRequiredPermissions(commandType)
    if !exists {
        logger.Warnf("Unknown command type: %s", commandType)
        return fmt.Errorf("unknown command type: %s", commandType)
    }

    logger.Debugf("Required permissions for command %s: %v", commandType, requiredPermissions)

    // Check if user has any of the required permissions for this device
    canAccess, err := userPermissions.CanAccessDevice(pg, deviceID, requiredPermissions...)
    if err != nil {
        logger.Errorf("Failed to check command permissions: %v", err)
        return fmt.Errorf("failed to check command permissions: %w", err)
    }

    if !canAccess {
        logger.Warnf("Permission denied: user=%s, device=%s, command=%s, required=%v",
            userPermissions.UserID, deviceID, commandType, requiredPermissions)
        return fmt.Errorf("insufficient permissions for command %s on device %s", commandType, deviceID)
    }

    logger.Infof("Command permission granted: user=%s, device=%s, command=%s",
        userPermissions.UserID, deviceID, commandType)
    return nil
}
```

#### 7.2 Metrics Collection
**File**: `microservices/rushhour/permissions/metrics.go`

```go
// Add metrics for permission checks
type PermissionMetrics struct {
    permissionChecksTotal    prometheus.Counter
    permissionChecksAllowed  prometheus.Counter
    permissionChecksDenied   prometheus.Counter
    permissionCheckDuration  prometheus.Histogram
}

// Track permission check metrics
func (cpm *CommandPermissionMapper) trackPermissionMetrics(
    commandType string,
    allowed bool,
    duration time.Duration,
) {
    cpm.metrics.permissionChecksTotal.Inc()
    if allowed {
        cpm.metrics.permissionChecksAllowed.Inc()
    } else {
        cpm.metrics.permissionChecksDenied.Inc()
    }
    cpm.metrics.permissionCheckDuration.Observe(duration.Seconds())
}
```

## Implementation Timeline

| Week | Phase | Tasks | Deliverables |
|------|-------|-------|--------------|
| 1 | Core Infrastructure | Command Permission Mapper, Updated Permission Checker | `command_mapper.go`, Updated `checker.go` |
| 2 | Binary Handler | Command validation in binary handler, protobuf integration | Updated `binary_handler.go` |
| 3 | Service Layer | Service integration, database connection | Updated `service.go` |
| 4 | Handler Layer | Handler updates, error handling | Updated `handler.go` |
| 5 | Main App | Main application integration | Updated `main.go` |
| 6 | Testing | Unit tests, integration tests | Test files |
| 7 | Monitoring | Logging, metrics, documentation | Monitoring tools, docs |

## Risk Mitigation

### Technical Risks
1. **Performance Impact**: Permission checks add database queries
   - **Mitigation**: Implement permission caching, batch permission checks
   
2. **Complexity**: Multiple permission types and scopes
   - **Mitigation**: Clear separation of concerns, comprehensive testing

3. **Backward Compatibility**: Existing clients may be affected
   - **Mitigation**: Gradual rollout, feature flags, backward compatibility tests

### Operational Risks
1. **Permission Misconfiguration**: Incorrect permission mappings
   - **Mitigation**: Automated testing, validation scripts, monitoring

2. **Database Dependencies**: Permission validation requires database access
   - **Mitigation**: Connection pooling, retry logic, fallback mechanisms

## Success Criteria

1. **Security**: Users cannot execute commands without proper permissions
2. **Performance**: Permission checks add <10ms latency to command processing
3. **Reliability**: 99.9% uptime for permission validation system
4. **Monitoring**: Real-time visibility into permission checks and failures
5. **Testing**: >90% test coverage for permission validation logic

## Future Enhancements

1. **Permission Caching**: Redis-based permission caching for improved performance
2. **Dynamic Permissions**: Runtime permission updates without service restart
3. **Audit Trail**: Comprehensive logging of all permission checks and decisions
4. **Permission Analytics**: Insights into permission usage patterns
5. **Role-Based Templates**: Predefined permission sets for common user roles

## Conclusion

This implementation provides a robust, scalable solution for command-level permission validation in the Socket.IO system. By leveraging the existing permission infrastructure and adding granular command validation, we ensure that users can only execute operations they're authorized to perform, significantly enhancing system security and compliance.

The phased approach allows for incremental testing and validation, reducing risk while ensuring all components work together seamlessly. The comprehensive testing strategy and monitoring capabilities provide confidence in the system's reliability and performance.
