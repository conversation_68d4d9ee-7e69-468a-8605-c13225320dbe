-- Set OrgType
INSERT INTO {{OrgType}} (Identifier, Name, Description, IsHidden) VALUES
  ('synapse',      'Synapse',      'Internal Synapse organization', true),  -- Internal org for Synapse
  ('municipality', 'Municipality', 'Municipality organization',     false), -- External org for municipalities
  ('oem',          'OEM',          'OEM organization',              false)  -- External org for OEMs
;
-- Set PermissionGroup
INSERT INTO {{PermissionGroup}} (Identifier, Scope, Name, Weight,Description) VALUES
  ('synapse',        'synapse',        'Synapse',        1.00, 'Permission sets only available to Synapse and its internal users'), -- Internal permission group for Synapse
  ('organization',   'org',            'Organization',   2.00, 'Permissions applied across the organization'), -- Permission group for organizations
  ('device_group',   'device_group',   'Device Group',   3.00, 'Permissions applied for device groups assigned to users'), -- Permission group for device groups
  ('location_group', 'location_group', 'Location Group', 4.00, 'Permissions applied for location groups assigned to users'), -- Permission group for location groups
  ('reports',        'org',            'Reports',        5.00, 'Permissions applied for reports') -- Permission group for reports
;
-- Set Permission
INSERT INTO {{Permission}} (Identifier, PermissionGroupIdentifier, OrgTypeIdentifier, Weight, Name, Description) VALUES
-- Synapse OrgType Permissions
  ('synapse_view_synapse_users',              'synapse',        'synapse',      1.00, 'View Synapse Users', 'Grants ability to view all Synapse users.'),
  ('synapse_manage_synapse_users',            'synapse',        'synapse',      2.00, 'Update Synapse Users', 'Grants ability to edit all Synapse users.'),
  ('synapse_delete_synapse_users',            'synapse',        'synapse',      3.00, 'Delete Synapse Users', 'Grants ability to delete all Synapse users.'),
  ('synapse_view_synapse_reports',            'synapse',        'synapse',      4.00, 'View Synapse Reports', 'Grants ability to view reports in Synapse.'),
  ('synapse_view_synapse_admin_reports',      'synapse',        'synapse',      5.00, 'View Synapse Admin Reports', 'Grants ability to view admin reports in Synapse.'),
  ('synapse_view_organizations',              'organization',   'synapse',      1.00, 'View All Organizations', 'Grants ability to view all organizations.'),
  ('synapse_manage_organizations',            'organization',   'synapse',      2.00, 'Update All Organizations', 'Grants ability to manage all organizations.'),
  ('synapse_delete_organizations',            'organization',   'synapse',      3.00, 'Delete All Organizations', 'Grants ability to delete all organizations.'),
  ('synapse_view_organization_users',         'organization',   'synapse',      4.00, 'View Organization Users', 'Grants ability to view all users in an organization.'),
  ('synapse_manage_organization_users',       'organization',   'synapse',      5.00, 'Update Organization Users', 'Grants ability to edit all users in an organization.'),
  ('synapse_delete_organization_users',       'organization',   'synapse',      6.00, 'Delete Organization Users', 'Grants ability to delete all users in an organization.'),
  ('synapse_view_organization_reports',       'reports',        'synapse',      1.00, 'View Organization Reports', 'Grants ability to view reports in an organization.'),
  ('synapse_view_organization_admin_reports', 'reports',        'synapse',      2.00, 'View Organization Admin Reports', 'Grants ability to view admin reports in an organization.'),
-- Municipality OrgType Permissions
  ('org_view_users',                          'organization',   'municipality', 1.00, 'View Users', 'Grants ability to view all users in the organization.'),
  ('org_manage_users',                        'organization',   'municipality', 2.00, 'Update Users', 'Grants ability to invite and edit users in the organization.'),
  ('org_delete_users',                        'organization',   'municipality', 3.00, 'Remove Users', 'Grants ability to remove users from the organization.'),
  ('org_view_settings',                       'organization',   'municipality', 4.00, 'View Settings', 'Grants ability to view organization-wide settings and preferences.'),
  ('org_manage_settings',                     'organization',   'municipality', 5.00, 'Update Settings', 'Grants ability to change organization-wide settings and preferences.'),
  ('org_manage_device_groups',                'organization',   'municipality', 6.00, 'Update Device Groups', 'Grants ability to create, edit, and delete device groups in the organization.'),
  ('org_manage_location_groups',              'organization',   'municipality', 7.00, 'Update Location Groups', 'Grants ability to create, edit, and delete location groups in the organization.'),
  ('org_view_devices',                        'organization',   'municipality', 8.00, 'View All Devices', 'Grants ability to view all devices in the organization.'),
  ('org_manage_devices',                      'organization',   'municipality', 9.00, 'Update All Devices', 'Grants ability to update operational fields on all devices in the organization.'),
  ('org_delete_devices',                      'organization',   'municipality', 10.00, 'Delete Devices', 'Grants ability to delete devices in the organization.'),
  ('org_view_reports',                        'reports',        'municipality', 1.00, 'View Reports', 'Grants ability to view reports in the organization.'),
  ('org_view_admin_reports',                  'reports',        'municipality', 2.00, 'View Admin Reports', 'Grants ability to view admin reports in the organization.'),
--('org_export_reports',                      'reports',        'municipality', 3.00, 'Export Reports', 'Allows exporting downloadable reports.'), -- Not implemented yet
  ('device_group_view_devices',               'device_group',   'municipality', 1.00, 'View Devices', 'Grants ability to view devices within a device group.'),
  ('device_group_manage_devices',             'device_group',   'municipality', 1.00, 'Update Devices', 'Grants ability to update operational fields on devices within a device group.'),
  ('device_group_delete_devices',             'device_group',   'municipality', 2.00, 'Delete Devices', 'Grants ability to delete devices within a device group.'),
  ('location_group_view_devices',             'location_group', 'municipality', 3.00, 'View Devices', 'Grants ability to view devices within a location group.'),
  ('location_group_manage_devices',           'location_group', 'municipality', 4.00, 'Update Devices', 'Grants ability to update operational fields on devices within a location group.'),
  ('location_group_delete_devices',           'location_group', 'municipality', 5.00, 'Delete Devices', 'Grants ability to delete devices within a location group.'),
-- Set permissions for next gen devices
-- Insert new permissions for organization group
  ('org_ng_all_access',          'organization', 'municipality', 1.00, 'All Access', 'All access to the organization.'),
  ('org_ng_read_stats',          'organization', 'municipality', 2.00, 'Read Stats', 'Read stats to the organization.'),
  ('org_ng_set_time',            'organization', 'municipality', 3.00, 'Set Time', 'Set time to the organization.'),
  ('org_ng_read_config',         'organization', 'municipality', 4.00, 'Read Config', 'Read configuration to the organization.'),
  ('org_ng_clear_logs',          'organization', 'municipality', 5.00, 'Clear Logs', 'Clear logs to the organization.'),
  ('org_ng_reset_stats',         'organization', 'municipality', 6.00, 'Reset Stats', 'Reset stats to the organization.'),
  ('org_ng_write_datakey',       'organization', 'municipality', 7.00, 'Write Data Key', 'Write data key to the organization.'),
  ('org_ng_write_user_settings', 'organization', 'municipality', 8.00, 'Write User Settings', 'Write user settings to the organization.'),
  ('org_ng_dfu',                 'organization', 'municipality', 9.00, 'DFU', 'DFU to the organization.'),
-- Insert new permissions for device group
  ('device_group_ng_all_access',          'device_group', 'municipality', 1.00, 'All Access', 'All access to the device group.'),
  ('device_group_ng_read_stats',          'device_group', 'municipality', 2.00, 'Read Stats', 'Read stats to the device group.'),
  ('device_group_ng_set_time',            'device_group', 'municipality', 3.00, 'Set Time', 'Set time to the device group.'),
  ('device_group_ng_read_config',         'device_group', 'municipality', 4.00, 'Read Config', 'Read configuration to the device group.'),
  ('device_group_ng_clear_logs',          'device_group', 'municipality', 5.00, 'Clear Logs', 'Clear logs to the device group.'),
  ('device_group_ng_reset_stats',         'device_group', 'municipality', 6.00, 'Reset Stats', 'Reset stats to the device group.'),
  ('device_group_ng_write_datakey',       'device_group', 'municipality', 7.00, 'Write Data Key', 'Write data key to the device group.'),
  ('device_group_ng_write_user_settings', 'device_group', 'municipality', 8.00, 'Write User Settings', 'Write user settings to the device group.'),
  ('device_group_ng_dfu',                 'device_group', 'municipality', 9.00, 'DFU', 'DFU to the device group.'),
-- Insert new permissions for location group
  ('location_group_ng_all_access',          'location_group', 'municipality', 1.00, 'All Access', 'All access to the location group.'),
  ('location_group_ng_read_stats',          'location_group', 'municipality', 2.00, 'Read Stats', 'Read stats to the location group.'),
  ('location_group_ng_set_time',            'location_group', 'municipality', 3.00, 'Set Time', 'Set time to the location group.'),
  ('location_group_ng_read_config',         'location_group', 'municipality', 4.00, 'Read Config', 'Read configuration to the location group.'),
  ('location_group_ng_clear_logs',          'location_group', 'municipality', 5.00, 'Clear Logs', 'Clear logs to the location group.'),
  ('location_group_ng_reset_stats',         'location_group', 'municipality', 6.00, 'Reset Stats', 'Reset stats to the location group.'),
  ('location_group_ng_write_datakey',       'location_group', 'municipality', 7.00, 'Write Data Key', 'Write data key to the location group.'),
  ('location_group_ng_write_user_settings', 'location_group', 'municipality', 8.00, 'Write User Settings', 'Write user settings to the location group.'),
  ('location_group_ng_dfu',                 'location_group', 'municipality', 9.00, 'DFU', 'DFU to the location group.');
-- Set TemplateRole
INSERT INTO {{TemplateRole}} (Identifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
-- Municipality permissions
  ('mun_admin',       'municipality', 'Admin',             'Administrative permissions', FALSE),
  ('mun_manager',     'municipality', 'Manager',           'Manager permissions', TRUE),
  ('mun_technician',  'municipality', 'Signal Technician', 'Technician permissions', TRUE),
  ('mun_anonymous',   'municipality', 'Anonymous',         'Deny all permissions', FALSE),
-- Synapse permissions
  ('syn_admin',       'synapse', 'Admin',       'Administrative permissions', FALSE),
  ('syn_manager',     'synapse', 'Manager',     'Manager permissions', TRUE),
  ('syn_onboarder',   'synapse', 'Onboarder',   'Onboarding permissions', TRUE),
  ('syn_anonymous',   'synapse', 'Anonymous',   'Deny all permissions', FALSE),
-- Next Gen Device permissions
  ('mun_traffic_engineers',        'municipality', 'Traffic Engineers',        'Traffic engineering personnel with device access', true),
  ('mun_construction_contractors', 'municipality', 'Construction Contractors', 'Construction contractors with device access',      true),
  ('mun_maintenance_contractors',  'municipality', 'Maintenance Contractors',  'Maintenance contractors with device access',       true),
  ('mun_it',                       'municipality', 'IT',                       'IT personnel with device access',                  true),
  ('mun_consultant',               'municipality', 'Consultant',               'Consultants with device access',                   true);
-- Set TemplateRolePermission
INSERT INTO {{TemplateRolePermission}} (TemplateRoleIdentifier, PermissionIdentifier, DefaultValue) VALUES
-- municipality:admin permissions (full access)
  ('mun_admin', 'org_view_users',                    true),
  ('mun_admin', 'org_manage_users',                  true),
  ('mun_admin', 'org_delete_users',                  true),
  ('mun_admin', 'org_view_settings',                 true),
  ('mun_admin', 'org_manage_settings',               true),
  ('mun_admin', 'org_manage_device_groups',          true),
  ('mun_admin', 'org_manage_location_groups',        true),
  ('mun_admin', 'org_view_devices',                  true),
  ('mun_admin', 'org_manage_devices',                true),
  ('mun_admin', 'org_delete_devices',                true),
  ('mun_admin', 'org_view_reports',                  true),
  ('mun_admin', 'org_view_admin_reports',            true),
  ('mun_admin', 'device_group_view_devices',         true),
  ('mun_admin', 'device_group_manage_devices',       true),
  ('mun_admin', 'device_group_delete_devices',       true),
  ('mun_admin', 'location_group_view_devices',       true),
  ('mun_admin', 'location_group_manage_devices',     true),
  ('mun_admin', 'location_group_delete_devices',     true),
-- municipality:manager permissions (limited delete access)
  ('mun_manager', 'org_view_users',                  true),
  ('mun_manager', 'org_manage_users',                true),
  ('mun_manager', 'org_delete_users',                false),
  ('mun_manager', 'org_view_settings',               true),
  ('mun_manager', 'org_manage_settings',             false),
  ('mun_manager', 'org_manage_device_groups',        true),
  ('mun_manager', 'org_manage_location_groups',      true),
  ('mun_manager', 'org_view_devices',                true),
  ('mun_manager', 'org_manage_devices',              true),
  ('mun_manager', 'org_delete_devices',              false),
  ('mun_manager', 'org_view_reports',                true),
  ('mun_manager', 'org_view_admin_reports',          true),
  ('mun_manager', 'device_group_view_devices',       true),
  ('mun_manager', 'device_group_manage_devices',     true),
  ('mun_manager', 'device_group_delete_devices',     false),
  ('mun_manager', 'location_group_view_devices',     true),
  ('mun_manager', 'location_group_manage_devices',   true),
  ('mun_manager', 'location_group_delete_devices',   false),
-- municipality:technician permissions (device focused)
  ('mun_technician', 'org_view_users',               false),
  ('mun_technician', 'org_manage_users',             false),
  ('mun_technician', 'org_delete_users',             false),
  ('mun_technician', 'org_view_settings',            false),
  ('mun_technician', 'org_manage_settings',          false),
  ('mun_technician', 'org_manage_device_groups',     false),
  ('mun_technician', 'org_manage_location_groups',   false),
  ('mun_technician', 'org_view_devices',             true),
  ('mun_technician', 'org_manage_devices',           false),
  ('mun_technician', 'org_delete_devices',           false),
  ('mun_technician', 'org_view_reports',             true),
  ('mun_technician', 'org_view_admin_reports',       false),
  ('mun_technician', 'device_group_view_devices',    true),
  ('mun_technician', 'device_group_manage_devices',  true),
  ('mun_technician', 'device_group_delete_devices',  false),
  ('mun_technician', 'location_group_view_devices',  true),
  ('mun_technician', 'location_group_manage_devices', true),
  ('mun_technician', 'location_group_delete_devices', false),
-- municipality:anonymous permissions (no access)
  ('mun_anonymous', 'org_view_users',                false),
  ('mun_anonymous', 'org_manage_users',              false),
  ('mun_anonymous', 'org_delete_users',              false),
  ('mun_anonymous', 'org_view_settings',             false),
  ('mun_anonymous', 'org_manage_settings',           false),
  ('mun_anonymous', 'org_manage_device_groups',      false),
  ('mun_anonymous', 'org_manage_location_groups',    false),
  ('mun_anonymous', 'org_view_devices',              false),
  ('mun_anonymous', 'org_manage_devices',            false),
  ('mun_anonymous', 'org_delete_devices',            false),
  ('mun_anonymous', 'org_view_reports',              false),
  ('mun_anonymous', 'org_view_admin_reports',        false),
  ('mun_anonymous', 'device_group_view_devices',     false),
  ('mun_anonymous', 'device_group_manage_devices',   false),
  ('mun_anonymous', 'device_group_delete_devices',   false),
  ('mun_anonymous', 'location_group_view_devices',   false),
  ('mun_anonymous', 'location_group_manage_devices', false),
  ('mun_anonymous', 'location_group_delete_devices', false),
-- synapse:admin permissions (full synapse access)
  ('syn_admin', 'synapse_view_synapse_users',              true),
  ('syn_admin', 'synapse_manage_synapse_users',            true),
  ('syn_admin', 'synapse_delete_synapse_users',            true),
  ('syn_admin', 'synapse_view_synapse_reports',            true),
  ('syn_admin', 'synapse_view_synapse_admin_reports',      true),
  ('syn_admin', 'synapse_view_organizations',              true),
  ('syn_admin', 'synapse_manage_organizations',            true),
  ('syn_admin', 'synapse_delete_organizations',            true),
  ('syn_admin', 'synapse_view_organization_users',         true),
  ('syn_admin', 'synapse_manage_organization_users',       true),
  ('syn_admin', 'synapse_delete_organization_users',       true),
  ('syn_admin', 'synapse_view_organization_reports',       true),
  ('syn_admin', 'synapse_view_organization_admin_reports', true),
-- synapse:manager permissions (limited delete access)
  ('syn_manager', 'synapse_view_synapse_users',              true),
  ('syn_manager', 'synapse_manage_synapse_users',            true),
  ('syn_manager', 'synapse_delete_synapse_users',            false),
  ('syn_manager', 'synapse_view_synapse_reports',            true),
  ('syn_manager', 'synapse_view_synapse_admin_reports',      false),
  ('syn_manager', 'synapse_view_organizations',              true),
  ('syn_manager', 'synapse_manage_organizations',            true),
  ('syn_manager', 'synapse_delete_organizations',            false),
  ('syn_manager', 'synapse_view_organization_users',         true),
  ('syn_manager', 'synapse_manage_organization_users',       true),
  ('syn_manager', 'synapse_delete_organization_users',       false),
  ('syn_manager', 'synapse_view_organization_reports',       true),
  ('syn_manager', 'synapse_view_organization_admin_reports', false),
-- synapse:onboarder permissions (onboarding focused)
  ('syn_onboarder', 'synapse_view_synapse_users',              false),
  ('syn_onboarder', 'synapse_manage_synapse_users',            false),
  ('syn_onboarder', 'synapse_delete_synapse_users',            false),
  ('syn_onboarder', 'synapse_view_synapse_reports',            true),
  ('syn_onboarder', 'synapse_view_synapse_admin_reports',      false),
  ('syn_onboarder', 'synapse_view_organizations',              true),
  ('syn_onboarder', 'synapse_manage_organizations',            false),
  ('syn_onboarder', 'synapse_delete_organizations',            false),
  ('syn_onboarder', 'synapse_view_organization_users',         true),
  ('syn_onboarder', 'synapse_manage_organization_users',       false),
  ('syn_onboarder', 'synapse_delete_organization_users',       false),
  ('syn_onboarder', 'synapse_view_organization_reports',       true),
  ('syn_onboarder', 'synapse_view_organization_admin_reports', false),
-- synapse:anonymous permissions (no access)
  ('syn_anonymous', 'synapse_view_synapse_users',              false),
  ('syn_anonymous', 'synapse_manage_synapse_users',            false),
  ('syn_anonymous', 'synapse_delete_synapse_users',            false),
  ('syn_anonymous', 'synapse_view_synapse_reports',            false),
  ('syn_anonymous', 'synapse_view_synapse_admin_reports',      false),
  ('syn_anonymous', 'synapse_view_organizations',              false),
  ('syn_anonymous', 'synapse_manage_organizations',            false),
  ('syn_anonymous', 'synapse_delete_organizations',            false),
  ('syn_anonymous', 'synapse_view_organization_users',         false),
  ('syn_anonymous', 'synapse_manage_organization_users',       false),
  ('syn_anonymous', 'synapse_delete_organization_users',       false),
  ('syn_anonymous', 'synapse_view_organization_reports',       false),
  ('syn_anonymous', 'synapse_view_organization_admin_reports', false),
-- Next Gen Device permissions
-- For Admin
  ('mun_admin', 'org_ng_all_access',                     true),
  ('mun_admin', 'org_ng_read_stats',                     true),
  ('mun_admin', 'org_ng_set_time',                       true),
  ('mun_admin', 'org_ng_read_config',                    true),
  ('mun_admin', 'org_ng_clear_logs',                     true),
  ('mun_admin', 'org_ng_reset_stats',                    true),
  ('mun_admin', 'org_ng_write_datakey',                  true),
  ('mun_admin', 'org_ng_write_user_settings',            true),
  ('mun_admin', 'org_ng_dfu',                            true),
  ('mun_admin', 'device_group_ng_all_access',            true),
  ('mun_admin', 'device_group_ng_read_stats',            true),
  ('mun_admin', 'device_group_ng_set_time',              true),
  ('mun_admin', 'device_group_ng_read_config',           true),
  ('mun_admin', 'device_group_ng_clear_logs',            true),
  ('mun_admin', 'device_group_ng_reset_stats',           true),
  ('mun_admin', 'device_group_ng_write_datakey',         true),
  ('mun_admin', 'device_group_ng_write_user_settings',   true),
  ('mun_admin', 'device_group_ng_dfu',                   true),
  ('mun_admin', 'location_group_ng_all_access',          true),
  ('mun_admin', 'location_group_ng_read_stats',          true),
  ('mun_admin', 'location_group_ng_set_time',            true),
  ('mun_admin', 'location_group_ng_read_config',         true),
  ('mun_admin', 'location_group_ng_clear_logs',          true),
  ('mun_admin', 'location_group_ng_reset_stats',         true),
  ('mun_admin', 'location_group_ng_write_datakey',       true),
  ('mun_admin', 'location_group_ng_write_user_settings', true),
  ('mun_admin', 'location_group_ng_dfu',                 true),
-- For Traffic Engineers
  ('mun_traffic_engineers', 'org_ng_all_access',                     true),
  ('mun_traffic_engineers', 'org_ng_read_stats',                     true),
  ('mun_traffic_engineers', 'org_ng_set_time',                       true),
  ('mun_traffic_engineers', 'org_ng_read_config',                    true),
  ('mun_traffic_engineers', 'org_ng_clear_logs',                     true),
  ('mun_traffic_engineers', 'org_ng_reset_stats',                    true),
  ('mun_traffic_engineers', 'org_ng_write_datakey',                  true),
  ('mun_traffic_engineers', 'org_ng_write_user_settings',            true),
  ('mun_traffic_engineers', 'org_ng_dfu',                            true),
  ('mun_traffic_engineers', 'device_group_ng_all_access',            true),
  ('mun_traffic_engineers', 'device_group_ng_read_stats',            true),
  ('mun_traffic_engineers', 'device_group_ng_set_time',              true),
  ('mun_traffic_engineers', 'device_group_ng_read_config',           true),
  ('mun_traffic_engineers', 'device_group_ng_clear_logs',            true),
  ('mun_traffic_engineers', 'device_group_ng_reset_stats',           true),
  ('mun_traffic_engineers', 'device_group_ng_write_datakey',         true),
  ('mun_traffic_engineers', 'device_group_ng_write_user_settings',   true),
  ('mun_traffic_engineers', 'device_group_ng_dfu',                   true),
  ('mun_traffic_engineers', 'location_group_ng_all_access',          true),
  ('mun_traffic_engineers', 'location_group_ng_read_stats',          true),
  ('mun_traffic_engineers', 'location_group_ng_set_time',            true),
  ('mun_traffic_engineers', 'location_group_ng_read_config',         true),
  ('mun_traffic_engineers', 'location_group_ng_clear_logs',          true),
  ('mun_traffic_engineers', 'location_group_ng_reset_stats',         true),
  ('mun_traffic_engineers', 'location_group_ng_write_datakey',       true),
  ('mun_traffic_engineers', 'location_group_ng_write_user_settings', true),
  ('mun_traffic_engineers', 'location_group_ng_dfu',                 true),
-- For Construction Contractors
  ('mun_construction_contractors', 'org_ng_all_access',                     true),
  ('mun_construction_contractors', 'org_ng_read_stats',                     true),
  ('mun_construction_contractors', 'org_ng_set_time',                       true),
  ('mun_construction_contractors', 'org_ng_read_config',                    true),
  ('mun_construction_contractors', 'org_ng_clear_logs',                     true),
  ('mun_construction_contractors', 'org_ng_reset_stats',                    true),
  ('mun_construction_contractors', 'org_ng_write_datakey',                 false),
  ('mun_construction_contractors', 'org_ng_write_user_settings',            true),
  ('mun_construction_contractors', 'org_ng_dfu',                            true),
  ('mun_construction_contractors', 'device_group_ng_all_access',            true),
  ('mun_construction_contractors', 'device_group_ng_read_stats',            true),
  ('mun_construction_contractors', 'device_group_ng_set_time',              true),
  ('mun_construction_contractors', 'device_group_ng_read_config',           true),
  ('mun_construction_contractors', 'device_group_ng_clear_logs',            true),
  ('mun_construction_contractors', 'device_group_ng_reset_stats',           true),
  ('mun_construction_contractors', 'device_group_ng_write_datakey',        false),
  ('mun_construction_contractors', 'device_group_ng_write_user_settings',   true),
  ('mun_construction_contractors', 'device_group_ng_dfu',                   true),
  ('mun_construction_contractors', 'location_group_ng_all_access',          true),
  ('mun_construction_contractors', 'location_group_ng_read_stats',          true),
  ('mun_construction_contractors', 'location_group_ng_set_time',            true),
  ('mun_construction_contractors', 'location_group_ng_read_config',         true),
  ('mun_construction_contractors', 'location_group_ng_clear_logs',          true),
  ('mun_construction_contractors', 'location_group_ng_reset_stats',         true),
  ('mun_construction_contractors', 'location_group_ng_write_datakey',      false),
  ('mun_construction_contractors', 'location_group_ng_write_user_settings', true),
  ('mun_construction_contractors', 'location_group_ng_dfu',                 true),
-- For Signal Technician
  ('mun_technician', 'org_ng_all_access',                     true),
  ('mun_technician', 'org_ng_read_stats',                     true),
  ('mun_technician', 'org_ng_set_time',                       true),
  ('mun_technician', 'org_ng_read_config',                    true),
  ('mun_technician', 'org_ng_clear_logs',                    false),
  ('mun_technician', 'org_ng_reset_stats',                   false),
  ('mun_technician', 'org_ng_write_datakey',                  true),
  ('mun_technician', 'org_ng_write_user_settings',            true),
  ('mun_technician', 'org_ng_dfu',                            true),
  ('mun_technician', 'device_group_ng_all_access',            true),
  ('mun_technician', 'device_group_ng_read_stats',            true),
  ('mun_technician', 'device_group_ng_set_time',              true),
  ('mun_technician', 'device_group_ng_read_config',           true),
  ('mun_technician', 'device_group_ng_clear_logs',           false),
  ('mun_technician', 'device_group_ng_reset_stats',          false),
  ('mun_technician', 'device_group_ng_write_datakey',         true),
  ('mun_technician', 'device_group_ng_write_user_settings',   true),
  ('mun_technician', 'device_group_ng_dfu',                   true),
  ('mun_technician', 'location_group_ng_all_access',          true),
  ('mun_technician', 'location_group_ng_read_stats',          true),
  ('mun_technician', 'location_group_ng_set_time',            true),
  ('mun_technician', 'location_group_ng_read_config',         true),
  ('mun_technician', 'location_group_ng_clear_logs',         false),
  ('mun_technician', 'location_group_ng_reset_stats',        false),
  ('mun_technician', 'location_group_ng_write_datakey',       true),
  ('mun_technician', 'location_group_ng_write_user_settings', true),
  ('mun_technician', 'location_group_ng_dfu',                 true),
-- For Maintenance Contractors
  ('mun_maintenance_contractors', 'org_ng_all_access',                     true),
  ('mun_maintenance_contractors', 'org_ng_read_stats',                     true),
  ('mun_maintenance_contractors', 'org_ng_set_time',                       true),
  ('mun_maintenance_contractors', 'org_ng_read_config',                    true),
  ('mun_maintenance_contractors', 'org_ng_clear_logs',                    false),
  ('mun_maintenance_contractors', 'org_ng_reset_stats',                   false),
  ('mun_maintenance_contractors', 'org_ng_write_datakey',                 false),
  ('mun_maintenance_contractors', 'org_ng_write_user_settings',            true),
  ('mun_maintenance_contractors', 'org_ng_dfu',                            true),
  ('mun_maintenance_contractors', 'device_group_ng_all_access',            true),
  ('mun_maintenance_contractors', 'device_group_ng_read_stats',            true),
  ('mun_maintenance_contractors', 'device_group_ng_set_time',              true),
  ('mun_maintenance_contractors', 'device_group_ng_read_config',           true),
  ('mun_maintenance_contractors', 'device_group_ng_clear_logs',           false),
  ('mun_maintenance_contractors', 'device_group_ng_reset_stats',          false),
  ('mun_maintenance_contractors', 'device_group_ng_write_datakey',        false),
  ('mun_maintenance_contractors', 'device_group_ng_write_user_settings',   true),
  ('mun_maintenance_contractors', 'device_group_ng_dfu',                   true),
  ('mun_maintenance_contractors', 'location_group_ng_all_access',          true),
  ('mun_maintenance_contractors', 'location_group_ng_read_stats',          true),
  ('mun_maintenance_contractors', 'location_group_ng_set_time',            true),
  ('mun_maintenance_contractors', 'location_group_ng_read_config',         true),
  ('mun_maintenance_contractors', 'location_group_ng_clear_logs',         false),
  ('mun_maintenance_contractors', 'location_group_ng_reset_stats',        false),
  ('mun_maintenance_contractors', 'location_group_ng_write_datakey',      false),
  ('mun_maintenance_contractors', 'location_group_ng_write_user_settings', true),
  ('mun_maintenance_contractors', 'location_group_ng_dfu',                 true),
-- For IT
  ('mun_it', 'org_ng_all_access',                      true),
  ('mun_it', 'org_ng_read_stats',                     false),
  ('mun_it', 'org_ng_set_time',                        true),
  ('mun_it', 'org_ng_read_config',                    false),
  ('mun_it', 'org_ng_clear_logs',                     false),
  ('mun_it', 'org_ng_reset_stats',                    false),
  ('mun_it', 'org_ng_write_datakey',                  false),
  ('mun_it', 'org_ng_write_user_settings',            false),
  ('mun_it', 'org_ng_dfu',                            false),
  ('mun_it', 'device_group_ng_all_access',             true),
  ('mun_it', 'device_group_ng_read_stats',            false),
  ('mun_it', 'device_group_ng_set_time',               true),
  ('mun_it', 'device_group_ng_read_config',           false),
  ('mun_it', 'device_group_ng_clear_logs',            false),
  ('mun_it', 'device_group_ng_reset_stats',           false),
  ('mun_it', 'device_group_ng_write_datakey',         false),
  ('mun_it', 'device_group_ng_write_user_settings',   false),
  ('mun_it', 'device_group_ng_dfu',                   false),
  ('mun_it', 'location_group_ng_all_access',           true),
  ('mun_it', 'location_group_ng_read_stats',          false),
  ('mun_it', 'location_group_ng_set_time',             true),
  ('mun_it', 'location_group_ng_read_config',         false),
  ('mun_it', 'location_group_ng_clear_logs',          false),
  ('mun_it', 'location_group_ng_reset_stats',         false),
  ('mun_it', 'location_group_ng_write_datakey',       false),
  ('mun_it', 'location_group_ng_write_user_settings', false),
  ('mun_it', 'location_group_ng_dfu',                 false),
-- For Consultant
  ('mun_consultant', 'org_ng_all_access',                      true),
  ('mun_consultant', 'org_ng_read_stats',                     false),
  ('mun_consultant', 'org_ng_set_time',                       false),
  ('mun_consultant', 'org_ng_read_config',                     true),
  ('mun_consultant', 'org_ng_clear_logs',                     false),
  ('mun_consultant', 'org_ng_reset_stats',                    false),
  ('mun_consultant', 'org_ng_write_datakey',                  false),
  ('mun_consultant', 'org_ng_write_user_settings',            false),
  ('mun_consultant', 'org_ng_dfu',                            false),
  ('mun_consultant', 'device_group_ng_all_access',             true),
  ('mun_consultant', 'device_group_ng_read_stats',            false),
  ('mun_consultant', 'device_group_ng_set_time',              false),
  ('mun_consultant', 'device_group_ng_read_config',            true),
  ('mun_consultant', 'device_group_ng_clear_logs',            false),
  ('mun_consultant', 'device_group_ng_reset_stats',           false),
  ('mun_consultant', 'device_group_ng_write_datakey',         false),
  ('mun_consultant', 'device_group_ng_write_user_settings',   false),
  ('mun_consultant', 'device_group_ng_dfu',                   false),
  ('mun_consultant', 'location_group_ng_all_access',           true),
  ('mun_consultant', 'location_group_ng_read_stats',          false),
  ('mun_consultant', 'location_group_ng_set_time',            false),
  ('mun_consultant', 'location_group_ng_read_config',          true),
  ('mun_consultant', 'location_group_ng_clear_logs',          false),
  ('mun_consultant', 'location_group_ng_reset_stats',         false),
  ('mun_consultant', 'location_group_ng_write_datakey',       false),
  ('mun_consultant', 'location_group_ng_write_user_settings', false),
  ('mun_consultant', 'location_group_ng_dfu',                 false),
-- For new roles:
-- Grant true to org_view_devices, org_manage_devices, device_group_view_devices,
-- device_group_manage_devices, location_group_view_devices, and location_group_manage_devices
-- Traffic Engineers
  ('mun_traffic_engineers', 'org_view_devices',               true),
  ('mun_traffic_engineers', 'org_manage_devices',             true),
  ('mun_traffic_engineers', 'device_group_view_devices',      true),
  ('mun_traffic_engineers', 'device_group_manage_devices',    true),
  ('mun_traffic_engineers', 'location_group_view_devices',    true),
  ('mun_traffic_engineers', 'location_group_manage_devices',  true),
  ('mun_traffic_engineers', 'org_view_users',                false),
  ('mun_traffic_engineers', 'org_manage_users',              false),
  ('mun_traffic_engineers', 'org_delete_users',              false),
  ('mun_traffic_engineers', 'org_view_settings',             false),
  ('mun_traffic_engineers', 'org_manage_settings',           false),
  ('mun_traffic_engineers', 'org_manage_device_groups',      false),
  ('mun_traffic_engineers', 'org_manage_location_groups',    false),
  ('mun_traffic_engineers', 'org_delete_devices',            false),
  ('mun_traffic_engineers', 'org_view_reports',              false),
  ('mun_traffic_engineers', 'org_view_admin_reports',        false),
  ('mun_traffic_engineers', 'device_group_delete_devices',   false),
  ('mun_traffic_engineers', 'location_group_delete_devices', false),
-- Construction Contractors
  ('mun_construction_contractors', 'org_view_devices',               true),
  ('mun_construction_contractors', 'org_manage_devices',             true),
  ('mun_construction_contractors', 'device_group_view_devices',      true),
  ('mun_construction_contractors', 'device_group_manage_devices',    true),
  ('mun_construction_contractors', 'location_group_view_devices',    true),
  ('mun_construction_contractors', 'location_group_manage_devices',  true),
  ('mun_construction_contractors', 'org_view_users',                false),
  ('mun_construction_contractors', 'org_manage_users',              false),
  ('mun_construction_contractors', 'org_delete_users',              false),
  ('mun_construction_contractors', 'org_view_settings',             false),
  ('mun_construction_contractors', 'org_manage_settings',           false),
  ('mun_construction_contractors', 'org_manage_device_groups',      false),
  ('mun_construction_contractors', 'org_manage_location_groups',    false),
  ('mun_construction_contractors', 'org_delete_devices',            false),
  ('mun_construction_contractors', 'org_view_reports',              false),
  ('mun_construction_contractors', 'org_view_admin_reports',        false),
  ('mun_construction_contractors', 'device_group_delete_devices',   false),
  ('mun_construction_contractors', 'location_group_delete_devices', false),
-- Maintenance Contractors
  ('mun_maintenance_contractors', 'org_view_devices',               true),
  ('mun_maintenance_contractors', 'org_manage_devices',             true),
  ('mun_maintenance_contractors', 'device_group_view_devices',      true),
  ('mun_maintenance_contractors', 'device_group_manage_devices',    true),
  ('mun_maintenance_contractors', 'location_group_view_devices',    true),
  ('mun_maintenance_contractors', 'location_group_manage_devices',  true),
  ('mun_maintenance_contractors', 'org_view_users',                false),
  ('mun_maintenance_contractors', 'org_manage_users',              false),
  ('mun_maintenance_contractors', 'org_delete_users',              false),
  ('mun_maintenance_contractors', 'org_view_settings',             false),
  ('mun_maintenance_contractors', 'org_manage_settings',           false),
  ('mun_maintenance_contractors', 'org_manage_device_groups',      false),
  ('mun_maintenance_contractors', 'org_manage_location_groups',    false),
  ('mun_maintenance_contractors', 'org_delete_devices',            false),
  ('mun_maintenance_contractors', 'org_view_reports',              false),
  ('mun_maintenance_contractors', 'org_view_admin_reports',        false),
  ('mun_maintenance_contractors', 'device_group_delete_devices',   false),
  ('mun_maintenance_contractors', 'location_group_delete_devices', false),
-- IT
  ('mun_it', 'org_view_devices',               true),
  ('mun_it', 'org_manage_devices',             true),
  ('mun_it', 'device_group_view_devices',      true),
  ('mun_it', 'device_group_manage_devices',    true),
  ('mun_it', 'location_group_view_devices',    true),
  ('mun_it', 'location_group_manage_devices',  true),
  ('mun_it', 'org_view_users',                false),
  ('mun_it', 'org_manage_users',              false),
  ('mun_it', 'org_delete_users',              false),
  ('mun_it', 'org_view_settings',             false),
  ('mun_it', 'org_manage_settings',           false),
  ('mun_it', 'org_manage_device_groups',      false),
  ('mun_it', 'org_manage_location_groups',    false),
  ('mun_it', 'org_delete_devices',            false),
  ('mun_it', 'org_view_reports',              false),
  ('mun_it', 'org_view_admin_reports',        false),
  ('mun_it', 'device_group_delete_devices',   false),
  ('mun_it', 'location_group_delete_devices', false),
-- Consultant
  ('mun_consultant', 'org_view_devices',               true),
  ('mun_consultant', 'org_manage_devices',             true),
  ('mun_consultant', 'device_group_view_devices',      true),
  ('mun_consultant', 'device_group_manage_devices',    true),
  ('mun_consultant', 'location_group_view_devices',    true),
  ('mun_consultant', 'location_group_manage_devices',  true),
  ('mun_consultant', 'org_view_users',                false),
  ('mun_consultant', 'org_manage_users',              false),
  ('mun_consultant', 'org_delete_users',              false),
  ('mun_consultant', 'org_view_settings',             false),
  ('mun_consultant', 'org_manage_settings',           false),
  ('mun_consultant', 'org_manage_device_groups',      false),
  ('mun_consultant', 'org_manage_location_groups',    false),
  ('mun_consultant', 'org_delete_devices',            false),
  ('mun_consultant', 'org_view_reports',              false),
  ('mun_consultant', 'org_view_admin_reports',        false),
  ('mun_consultant', 'device_group_delete_devices',   false),
  ('mun_consultant', 'location_group_delete_devices', false),
-- For Manager role
  ('mun_manager', 'org_ng_all_access',                      true),
  ('mun_manager', 'org_ng_read_stats',                     false),
  ('mun_manager', 'org_ng_set_time',                       false),
  ('mun_manager', 'org_ng_read_config',                    false),
  ('mun_manager', 'org_ng_clear_logs',                     false),
  ('mun_manager', 'org_ng_reset_stats',                    false),
  ('mun_manager', 'org_ng_write_datakey',                  false),
  ('mun_manager', 'org_ng_write_user_settings',            false),
  ('mun_manager', 'org_ng_dfu',                            false),
  ('mun_manager', 'device_group_ng_all_access',             true),
  ('mun_manager', 'device_group_ng_read_stats',            false),
  ('mun_manager', 'device_group_ng_set_time',              false),
  ('mun_manager', 'device_group_ng_read_config',           false),
  ('mun_manager', 'device_group_ng_clear_logs',            false),
  ('mun_manager', 'device_group_ng_reset_stats',           false),
  ('mun_manager', 'device_group_ng_write_datakey',         false),
  ('mun_manager', 'device_group_ng_write_user_settings',   false),
  ('mun_manager', 'device_group_ng_dfu',                   false),
  ('mun_manager', 'location_group_ng_all_access',           true),
  ('mun_manager', 'location_group_ng_read_stats',          false),
  ('mun_manager', 'location_group_ng_set_time',            false),
  ('mun_manager', 'location_group_ng_read_config',         false),
  ('mun_manager', 'location_group_ng_clear_logs',          false),
  ('mun_manager', 'location_group_ng_reset_stats',         false),
  ('mun_manager', 'location_group_ng_write_datakey',       false),
  ('mun_manager', 'location_group_ng_write_user_settings', false),
  ('mun_manager', 'location_group_ng_dfu',                 false),
-- For Anonymous role
  ('mun_anonymous', 'org_ng_all_access',                      true),
  ('mun_anonymous', 'org_ng_read_stats',                     false),
  ('mun_anonymous', 'org_ng_set_time',                       false),
  ('mun_anonymous', 'org_ng_read_config',                    false),
  ('mun_anonymous', 'org_ng_clear_logs',                     false),
  ('mun_anonymous', 'org_ng_reset_stats',                    false),
  ('mun_anonymous', 'org_ng_write_datakey',                  false),
  ('mun_anonymous', 'org_ng_write_user_settings',            false),
  ('mun_anonymous', 'org_ng_dfu',                            false),
  ('mun_anonymous', 'device_group_ng_all_access',             true),
  ('mun_anonymous', 'device_group_ng_read_stats',            false),
  ('mun_anonymous', 'device_group_ng_set_time',              false),
  ('mun_anonymous', 'device_group_ng_read_config',           false),
  ('mun_anonymous', 'device_group_ng_clear_logs',            false),
  ('mun_anonymous', 'device_group_ng_reset_stats',           false),
  ('mun_anonymous', 'device_group_ng_write_datakey',         false),
  ('mun_anonymous', 'device_group_ng_write_user_settings',   false),
  ('mun_anonymous', 'device_group_ng_dfu',                   false),
  ('mun_anonymous', 'location_group_ng_all_access',           true),
  ('mun_anonymous', 'location_group_ng_read_stats',          false),
  ('mun_anonymous', 'location_group_ng_set_time',            false),
  ('mun_anonymous', 'location_group_ng_read_config',         false),
  ('mun_anonymous', 'location_group_ng_clear_logs',          false),
  ('mun_anonymous', 'location_group_ng_reset_stats',         false),
  ('mun_anonymous', 'location_group_ng_write_datakey',       false),
  ('mun_anonymous', 'location_group_ng_write_user_settings', false),
  ('mun_anonymous', 'location_group_ng_dfu',                 false)
;
-- Set available instruction statuses
INSERT INTO {{SoftwareGatewayInstructionStatus}} (Status) VALUES 
  ('queued'), 
  ('received');
-- Set available instructions
INSERT INTO {{SoftwareGatewayInstructionList}} (Instruction) VALUES 
  ('get_device_logs');
-- Create a Synapse organization
INSERT INTO {{Organization}} (Id, Name, Description, OrgTypeIdentifier) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'Synapse', 'Synapse-Its.com Organization', 'synapse');
-- Create the Custom Roles for the Synapse organization, no permission overrides.
INSERT INTO {{CustomRole}} (Id, OrganizationId, TemplateRoleIdentifier, OrgTypeIdentifier, Name, Description, IsDeletable) VALUES
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_admin'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'syn_admin', 'synapse', 'Synapse Admin', 'Synapse Admin Role', FALSE),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_manager'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'syn_manager', 'synapse', 'Synapse Manager', 'Synapse Manager Role', TRUE),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_onboarder'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'syn_onboarder', 'synapse', 'Synapse Onboarder', 'Synapse Onboarder Role', TRUE),
  (uuid_generate_v5(uuid_nil(), 'SYNAPSE_role_synapse_anonymous'), uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_synapse'), 'syn_anonymous', 'synapse', 'Synapse Anonymous', 'Synapse Anonymous Role', FALSE);
-- Set available device types
INSERT INTO {{DeviceType}} (Type, Description) VALUES 
  ('UNKNOWN_DEVICE', 'Unknown device type'), 
  ('EDI_LEGACY', 'EDI Legacy device'), 
  ('EDI_NEXT_GEN', 'EDI Next Gen device');

-- Set available gateway config template base settings CAT-508
INSERT INTO {{GatewayConfigTemplateBaseSettings}} (Setting, DefaultValue, Name, Description, Format)
VALUES
  ('channel_state_send_frequency_milliseconds',
   '500',
   'Channel State Send Frequency Milliseconds',
   'Frequency to send channel state to Firebase. In the communication setup for a device, the configuration for whether or not the device is enabled to send realtime data is set there. In other words, the device must be enabled for the frequency to take hold.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('send_rmsStatus_to_cloud',
   'true',
   'Send RMS Status To Cloud',
   'Flag indicating whether or not to send rmsStatus from legacy EDI devices to the GCP.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('rmsStatus_send_frequency_milliseconds',
   '500',
   'RMS Status Send Frequency Milliseconds',
   'Frequency to send rmsData to GCP.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('channel_state_batch_size',
   '500',
   'Channel State Batch Size',
   'The max number of devices to include in a batch sent to the cloud. The batch size is applicable to the following message-types: rmsEngine, monitorName, macAddress, rmsData.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('device_state_send_frequency_seconds',
   '60',
   'Device State Send Frequency Seconds',
   'Frequency to send rmsEngine, monitorName, macAddress information to the cloud. Note: This information is sent when the gateway-app launches or when a device goes offline and then back online. You can view this as the amount of time to wait before delivering the latest changes (if any) to the cloud.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('send_gateway_performance_stats_to_cloud',
   'true',
   'Send Gateway Performance Stats To Cloud',
   'Flag for sending gateway-app performance statistics to the cloud. Note: related to gateway_performance_stats_output_frequency_seconds.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('gateway_performance_stats_output_frequency_seconds',
   '60',
   'Gateway Performance Stats Output Frequency Seconds',
   'The amount of time to wait before sending the gateway-app performance statistics to the cloud.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('send_gateway_logs_to_cloud',
   'true',
   'Send Gateway Logs To Cloud',
   'Flag for sending gateway-logs to the cloud. Note: related to gateway_logs_output_frequency_seconds.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('gateway_logs_output_frequency_seconds',
   '3600',
   'Gateway Logs Output Frequency Seconds',
   'The amount of time to wait before sending the gateway-app log to the cloud.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('gateway_proxy_port_for_eccom_communication',
   '10001',
   'Gateway Proxy Port For Eccom Communication',
   'The port the ECCOM app uses for communication with the EDI device when the gateway-app is acting as a proxy.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('config_change_check_frequency_seconds',
   '30',
   'Config Change Check Frequency Seconds',
   'The amount of time the gateway-app waits before checking to see if there is a configuration update. If one is detected, the gateway-app will reset itself and download the new configuration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('edi_device_persist_connection',
   'true',
   'EDI Device Persist Connection',
   'Keep the TCP connection alive (i.e., do not open and close the connection). Note: False no longer works and is technically not an option.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('edi_device_processing_retries',
   '5',
   'EDI Device Processing Retries',
   'Number of consecutive attempts to communicate with a device before determining there is an error.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('log_compress_backups',
   'true',
   'Log Compress Backups',
   'Compress backups to save diskspace.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('log_file_max_size_mb',
   '1',
   'Log File Max Size MB',
   'The maximum size of the log file.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('log_filename',
   'log/gateway-app.log',
   'Log Filename',
   'The name of the active log.',
   '{"type":"string","versions":["v3"]}'
  ),
  ('log_level',
   'error',
   'Log Level',
   'The detail of the log. "error" being the least amount of info while "debug" will fill up quite fast. Defaults to "info" if the setting is not recognized.',
   '{"type":"string","versions":["v3"]}'
  ),
  ('log_max_age_in_days',
   '30',
   'Log Max Age In Days',
   'The max age of a log before it is deleted.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('log_max_backups',
   '10',
   'Log Max Backups',
   'The maximum number of backups before they get truncated.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('debug_level_device_ids',
   '[]',
   'Debug Level Device IDs',
   'The device identifiers to emit log information for.',
   '{"type":"array","versions":["v3"]}'
  ),
  ('record_http_requests_to_folder',
   'false',
   'Record HTTP Requests To Folder',
   'Logs all communications to devices and all HTTP communications to the /logs folder.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('threshold_device_error_seconds',
   '90',
   'Threshold Device Error Seconds',
   'The amount of time to wait before determining the device is in an error state.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('ws_active',
   'true',
   'WS Active',
   'Flag for turning on/off the web socket port for local integration.',
   '{"type":"boolean","versions":["v3"]}'
  ),
  ('ws_endpoint',
   '/gateway',
   'WS Endpoint',
   'The web socket endpoint for local integration.',
   '{"type":"string","versions":["v3"]}'
  ),
  ('ws_heartbeat_send_frequency_milliseconds',
   '60000',
   'WS Heartbeat Send Frequency Milliseconds',
   'The frequency to deliver heartbeat messages over the web socket endpoint for local integration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('ws_max_connections',
   '2',
   'WS Max Connections',
   'The maximum number of connections to allow on the web socket port for local integration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('ws_port',
   '8079',
   'WS Port',
   'The web socket port used for local integration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('ws_send_frequency_milliseconds',
   '5000',
   'WS Send Frequency Milliseconds',
   'The max amount of time to wait before checking for and sending device updates on the web socket connection for local integration.',
   '{"type":"integer","versions":["v3"]}'
  ),
  ('application_version',
   'deprecated',
   'Application Version',
   'No longer used.',
   '{"type":"string","versions":["v3"]}'
  ),
  ('software_update_check_frequency_seconds',
   '43200',
   'Software Update Check Frequency Seconds',
   'No longer used.',
   '{"type":"integer","versions":["v3"]}'
  );

WITH org AS (
  SELECT
    Id   AS organizationId,
    Name AS organizationName
  FROM {{Organization}}
)
INSERT INTO {{GatewayConfigTemplate}} (
  Id,
  Name,
  OrganizationId,
  Description
)
SELECT
  -- generate a v5 UUID whose "name" embeds the org’s UUID text
  uuid_generate_v5(
    uuid_nil(),
    format(
      'SYNAPSE_softwaregatewayconfigtemplate_%s',
      organizationId::text
    )
  ) AS Id,

  format(
    'Default Software Gateway Template for %s',
    organizationName
  ) AS Name,

  organizationId,

  'Default configuration for software gateways' AS Description

FROM org;

INSERT INTO {{GatewayConfigTemplateSettings}}
  (gatewayConfigTemplateId, setting, value)
SELECT
  t.Id              AS gatewayConfigTemplateId,
  b.Setting          AS setting,
  b.DefaultValue     AS value
FROM
  {{GatewayConfigTemplate}}           AS t
CROSS JOIN
  {{GatewayConfigTemplateBaseSettings}} AS b;
  
UPDATE {{SoftwareGateway}} 
SET TemplateId = uuid_generate_v5(
  uuid_nil(), 
  format('SYNAPSE_softwaregatewayconfigtemplate_%s', OrganizationId::text)
);

-- THESE MUST ALWAYS BE LAST
SELECT setval('{{Device_OrigId_SEQ}}', coalesce((select max(origid) from {{Device}}),1));
SELECT setval('{{User_OrigId_SEQ}}', coalesce((select max(origid) from {{User}}),1));
