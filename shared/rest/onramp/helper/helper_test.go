package helper

import (
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
)

// Test ValidateIdentifier function
func Test_ValidateIdentifier(t *testing.T) {
	t.<PERSON>lle<PERSON>()

	tests := []struct {
		name        string
		identifier  string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid identifier",
			identifier:  "valid-org-123",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty identifier",
			identifier:  "",
			expectedErr: ErrInvalidIdentifier,
			wantErr:     true,
		},
		{
			name:        "whitespace only identifier",
			identifier:  "   ",
			expectedErr: ErrInvalidIdentifier,
			wantErr:     true,
		},
		{
			name:        "identifier with spaces",
			identifier:  "  valid-org-123  ",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.<PERSON>()

			// Execute the function under test
			err := ValidateIdentifier(tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_ValidateOrganizationID tests the ValidateOrganizationID function
func Test_ValidateOrganizationID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		orgID       string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_organization_id",
			orgID:       "123e4567-e89b-12d3-a456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_organization_id",
			orgID:       "",
			expectedErr: ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_organization_id",
			orgID:       "   ",
			expectedErr: ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "invalid_uuid_format",
			orgID:       "invalid-uuid-format",
			expectedErr: ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "organization_id_with_spaces",
			orgID:       "  123e4567-e89b-12d3-a456-************  ",
			expectedErr: ErrInvalidOrganizationID,
			wantErr:     true,
		},
		{
			name:        "nil_uuid",
			orgID:       "00000000-0000-0000-0000-000000000000",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "different_valid_uuid",
			orgID:       "987fcdeb-51a2-43d1-b654-************",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the organization ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"organizationId": tt.orgID,
			})

			// Execute the function under test
			result, err := ValidateOrganizationID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.orgID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_ValidateInviteID tests the ValidateInviteID function
func Test_ValidateInviteID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		inviteID    string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_invite_id",
			inviteID:    "123e4567-e89b-12d3-a456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_invite_id",
			inviteID:    "",
			expectedErr: ErrInvalidInviteID,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_invite_id",
			inviteID:    "   ",
			expectedErr: ErrInvalidInviteID,
			wantErr:     true,
		},
		{
			name:        "invalid_uuid_format",
			inviteID:    "invalid-uuid-format",
			expectedErr: ErrInvalidInviteID,
			wantErr:     true,
		},
		{
			name:        "invite_id_with_spaces",
			inviteID:    "  123e4567-e89b-12d3-a456-************  ",
			expectedErr: ErrInvalidInviteID,
			wantErr:     true,
		},
		{
			name:        "nil_uuid",
			inviteID:    "00000000-0000-0000-0000-000000000000",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "different_valid_uuid",
			inviteID:    "987fcdeb-51a2-43d1-b654-************",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the invite ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"inviteId": tt.inviteID,
			})

			// Execute the function under test
			result, err := ValidateInviteID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.inviteID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_ValidateUserID tests the ValidateUserID function
func Test_ValidateUserID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		userID      string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid_user_id",
			userID:      "123e4567-e89b-12d3-a456-************",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty_user_id",
			userID:      "",
			expectedErr: ErrInvalidUserID,
			wantErr:     true,
		},
		{
			name:        "whitespace_only_user_id",
			userID:      "   ",
			expectedErr: ErrInvalidUserID,
			wantErr:     true,
		},
		{
			name:        "invalid_uuid_format",
			userID:      "invalid-uuid-format",
			expectedErr: ErrInvalidUserID,
			wantErr:     true,
		},
		{
			name:        "user_id_with_spaces",
			userID:      "  123e4567-e89b-12d3-a456-************  ",
			expectedErr: ErrInvalidUserID,
			wantErr:     true,
		},
		{
			name:        "nil_uuid",
			userID:      "00000000-0000-0000-0000-000000000000",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "different_valid_uuid",
			userID:      "987fcdeb-51a2-43d1-b654-************",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the user ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId": tt.userID,
			})

			// Execute the function under test
			result, err := ValidateUserID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.userID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_ValidateAuthMethodID tests the ValidateAuthMethodID function
func Test_ValidateAuthMethodID(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name         string
		authMethodID string
		expectedErr  error
		wantErr      bool
	}{
		{
			name:         "valid_auth_method_id",
			authMethodID: "123e4567-e89b-12d3-a456-************",
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:         "empty_auth_method_id",
			authMethodID: "",
			expectedErr:  ErrInvalidAuthMethodID,
			wantErr:      true,
		},
		{
			name:         "whitespace_only_auth_method_id",
			authMethodID: "   ",
			expectedErr:  ErrInvalidAuthMethodID,
			wantErr:      true,
		},
		{
			name:         "invalid_uuid_format",
			authMethodID: "invalid-uuid-format",
			expectedErr:  ErrInvalidAuthMethodID,
			wantErr:      true,
		},
		{
			name:         "auth_method_id_with_spaces",
			authMethodID: "  123e4567-e89b-12d3-a456-************  ",
			expectedErr:  ErrInvalidAuthMethodID,
			wantErr:      true,
		},
		{
			name:         "nil_uuid",
			authMethodID: "00000000-0000-0000-0000-000000000000",
			expectedErr:  nil,
			wantErr:      false,
		},
		{
			name:         "different_valid_uuid",
			authMethodID: "987fcdeb-51a2-43d1-b654-************",
			expectedErr:  nil,
			wantErr:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create a test request with the auth method ID
			req := httptest.NewRequest("GET", "/test", nil)
			req = mux.SetURLVars(req, map[string]string{
				"authMethodId": tt.authMethodID,
			})

			// Execute the function under test
			result, err := ValidateAuthMethodID(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Equal(t, uuid.Nil, result)
			} else {
				assert.NoError(t, err)

				// Parse the expected UUID to compare
				expectedUUID, parseErr := uuid.Parse(tt.authMethodID)
				if parseErr == nil {
					assert.Equal(t, expectedUUID, result)
				}
			}
		})
	}
}

// Test_GenerateRandomTokenHex tests the GenerateRandomTokenHex function
func Test_GenerateRandomTokenHex(t *testing.T) {
	t.Parallel()

	// Mock the randRead function for testing
	originalRandRead := randRead
	defer func() { randRead = originalRandRead }()

	tests := []struct {
		name         string
		length       uint
		mockRandRead func([]byte) (int, error)
		expectedErr  error
		wantErr      bool
	}{
		{
			name:   "valid_length_even",
			length: 10,
			mockRandRead: func(b []byte) (int, error) {
				// Fill with some test data
				for i := range b {
					b[i] = byte(i)
				}
				return len(b), nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:   "valid_length_odd",
			length: 11,
			mockRandRead: func(b []byte) (int, error) {
				// Fill with some test data
				for i := range b {
					b[i] = byte(i)
				}
				return len(b), nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:   "zero_length",
			length: 0,
			mockRandRead: func(b []byte) (int, error) {
				return 0, nil
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:   "rand_read_error",
			length: 10,
			mockRandRead: func(b []byte) (int, error) {
				return 0, assert.AnError
			},
			expectedErr: assert.AnError,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Set up the mock
			if tt.mockRandRead != nil {
				randRead = tt.mockRandRead
			}

			// Execute the function under test
			result, err := GenerateRandomTokenHex(tt.length)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
				assert.Empty(t, result)
			} else {
				assert.NoError(t, err)

				// Check that the result has the expected length
				// For even lengths, result should be exactly length characters
				// For odd lengths, result should be length-1 characters (since we need 2 hex chars per byte)
				expectedLen := tt.length
				if tt.length%2 == 1 {
					expectedLen = tt.length - 1
				}
				assert.Len(t, result, int(expectedLen))
			}
		})
	}
}
