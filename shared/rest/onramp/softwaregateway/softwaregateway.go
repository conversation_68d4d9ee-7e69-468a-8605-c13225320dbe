package softwaregateway

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	GetConnections         func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	CreateSoftwareGateway  func(pg connect.DatabaseExecutor, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error)
	GetAllSoftwareGateways func(pg connect.DatabaseExecutor) (*[]SoftwareGateway, error)
	GetSoftwareGateway     func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error)
	UpdateSoftwareGateway  func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error)
	DeleteSoftwareGateway  func(pg connect.DatabaseExecutor, identifier uuid.UUID) error
}

// CreateHandlerWithDeps creates a new software gateway
func CreateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse request body with validation
		requestBody, err := parseCreatAndUpdateRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate organization id exists
		err = validateOrganizationIdExists(pg, requestBody.OrganizationId)
		if err != nil {
			if errors.Is(err, ErrOrganizationNotFound) {
				logger.Errorf("Organization id not found: %v", err)
				response.CreateNotFoundResponse(w)
				return
			}

			logger.Errorf("Error validating organization id: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Create software gateway using dependency injection
		softwareGateway, err := deps.CreateSoftwareGateway(pg, requestBody)
		if err != nil {
			logger.Errorf("Error creating software gateway: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the created software gateway
		response.CreateSuccessResponse(softwareGateway.ToResponse(), w)
	}
}

// This handler will get all avaiable software gateways
func GetAllHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Get all software gateways using dependency injection
		softwareGateways, err := deps.GetAllSoftwareGateways(pg)
		if err != nil {
			logger.Errorf("Error getting all software gateways: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Convert the software gateways to responses
		responses := make([]SoftwareGatewayResponse, len(*softwareGateways))
		for i, sg := range *softwareGateways {
			responses[i] = sg.ToResponse()
		}

		// Return the software gateways
		response.CreateSuccessResponse(responses, w)
	}
}

// This handler will get a software gateway by its identifier
func GetByIdentifierHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse identifier as UUID
		vars := mux.Vars(r)
		identifierStr := vars["identifier"]
		identifier, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("invalid identifier: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Get the software gateway by its identifier using dependency injection
		softwareGateway, err := deps.GetSoftwareGateway(pg, identifier)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayNotFound) {
				logger.Errorf("Software gateway not found: %s", identifier)
				response.CreateNotFoundResponse(w)
				return
			}

			logger.Errorf("Error getting software gateway: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Check if software gateway is nil (shouldn't happen but be defensive)
		if softwareGateway == nil {
			logger.Errorf("Software gateway is nil: %s", identifier)
			response.CreateNotFoundResponse(w)
			return
		}

		// Return the software gateway
		response.CreateSuccessResponse(softwareGateway.ToResponse(), w)
	}
}

// This handler will update a software gateway by its identifier
func UpdateHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse identifier as UUID
		vars := mux.Vars(r)
		identifierStr := vars["identifier"]
		identifier, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("invalid identifier: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Parse request body with validation
		requestBody, err := parseCreatAndUpdateRequest(r)
		if err != nil {
			logger.Errorf("Error parsing request body: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Validate organization id exists
		err = validateOrganizationIdExists(pg, requestBody.OrganizationId)
		if err != nil {
			if errors.Is(err, ErrOrganizationNotFound) {
				logger.Errorf("Organization id not found: %v", err)
				response.CreateNotFoundResponse(w)
				return
			}

			logger.Errorf("Error validating organization id: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Update the software gateway using dependency injection
		softwareGateway, err := deps.UpdateSoftwareGateway(pg, identifier, requestBody)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayNotFound) {
				logger.Errorf("Software gateway not found for update: %s", identifier)
				response.CreateNotFoundResponse(w)
				return
			}

			logger.Errorf("Error updating software gateway: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Check if software gateway is nil (shouldn't happen but be defensive)
		if softwareGateway == nil {
			logger.Errorf("Updated software gateway is nil: %s", identifier)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the updated software gateway
		response.CreateSuccessResponse(softwareGateway.ToResponse(), w)
	}
}

// This handler will delete a software gateway by its identifier
func DeleteHandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// Parse identifier as UUID
		vars := mux.Vars(r)
		identifierStr := vars["identifier"]
		identifier, err := uuid.Parse(identifierStr)
		if err != nil {
			logger.Errorf("invalid identifier: %v", err)
			response.CreateBadRequestResponse(w)
			return
		}

		// Delete the software gateway using dependency injection
		err = deps.DeleteSoftwareGateway(pg, identifier)
		if err != nil {
			if errors.Is(err, ErrSoftwareGatewayNotFound) {
				logger.Errorf("Software gateway not found: %s", identifier)
				response.CreateNotFoundResponse(w)
				return
			}

			logger.Errorf("Error deleting software gateway: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Return the deleted software gateway
		response.CreateSuccessResponse(nil, w)
	}
}

// CreateSoftwareGateway inserts a new software gateway
var createSoftwareGateway = func(pg connect.DatabaseExecutor, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
	machineKey := uuid.New().String()
	apiKey := uuid.New().String()
	now := time.Now().UTC()

	query := `
		INSERT INTO {{SoftwareGateway}} (
			OrganizationId,
			MachineKey,
			ApiKey,
			Token,
			DateLastCheckedIn,
			PushConfigOnNextCheck,
			IsEnabled,
			Name,
			Description,
			CreatedAt,
			UpdatedAt,
			IsDeleted
		)
		VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
		)
		RETURNING Id, OrganizationId, MachineKey, ApiKey, Token, GatewayVersion, DateLastCheckedIn, PushConfigOnNextCheck, IsEnabled, Config, Name, Description, CreatedAt, UpdatedAt, IsDeleted, TemplateId`

	var gateway SoftwareGateway
	err := pg.QueryRowStruct(&gateway, query, req.OrganizationId, machineKey, apiKey, "", now, false, req.IsEnabled, req.Name, req.Description, now, now, false)
	if err != nil {
		logger.Errorf("Failed to create software gateway: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &gateway, nil
}

// GetAllSoftwareGateways retrieves all non-deleted software gateways
var getAllSoftwareGateways = func(pg connect.DatabaseExecutor) (*[]SoftwareGateway, error) {
	query := `
		SELECT 
			Id, 
			OrganizationId,
			MachineKey, 
			ApiKey, 
			Token,
			GatewayVersion,
			DateLastCheckedIn,
			PushConfigOnNextCheck,
			IsEnabled,
			Config,
			Name,
			Description, 
			CreatedAt, 
			UpdatedAt, 
			IsDeleted,
			TemplateId
		FROM {{SoftwareGateway}}
		WHERE IsDeleted = false
		ORDER BY CreatedAt DESC`

	var gateways []SoftwareGateway
	err := pg.QueryGenericSlice(&gateways, query)
	if err != nil {
		logger.Errorf("Failed to get software gateways: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &gateways, nil
}

// GetSoftwareGateway retrieves a software gateway by identifier
var getSoftwareGatewayByIdentifier = func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error) {
	query := `
		SELECT 
			Id,
			OrganizationId,
			MachineKey,
			ApiKey,
			Token,
			GatewayVersion,
			DateLastCheckedIn,
			PushConfigOnNextCheck,
			IsEnabled,
			Config,
			Name,
			Description,
			CreatedAt,
			UpdatedAt,
			IsDeleted,
			TemplateId
		FROM {{SoftwareGateway}}
		WHERE Id = $1 AND IsDeleted = false`

	var gateway SoftwareGateway
	err := pg.QueryRowStruct(&gateway, query, identifier)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSoftwareGatewayNotFound
		}
		logger.Errorf("Failed to get software gateway by identifier: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &gateway, nil
}

// UpdateSoftwareGateway updates the software gateway
var updateSoftwareGateway = func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
	now := time.Now().UTC()

	query := `
		UPDATE {{SoftwareGateway}}
		SET 
			Name = $1,
			Description = $2, 
			OrganizationId = $3,
			IsEnabled = $4,
			UpdatedAt = $5
		WHERE 
			Id = $6 
			AND IsDeleted = false
		RETURNING Id, OrganizationId, MachineKey, ApiKey, Token, GatewayVersion, DateLastCheckedIn, PushConfigOnNextCheck, IsEnabled, Config, Name, Description, CreatedAt, UpdatedAt, IsDeleted, TemplateId`

	var gateway SoftwareGateway
	err := pg.QueryRowStruct(&gateway, query, req.Name, req.Description, req.OrganizationId, req.IsEnabled, now, identifier)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSoftwareGatewayNotFound
		}
		logger.Errorf("Failed to update software gateway: %v", err)
		return nil, fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return &gateway, nil
}

// DeleteSoftwareGateway performs a soft delete on the software gateway
var deleteSoftwareGateway = func(pg connect.DatabaseExecutor, identifier uuid.UUID) error {
	now := time.Now().UTC()

	query := `
		UPDATE {{SoftwareGateway}}
		SET 
			IsDeleted = true, 
			DeletedAt = $1
		WHERE 
			Id = $2 
			AND IsDeleted = false`

	result, err := pg.Exec(query, now, identifier)
	if err != nil {
		logger.Errorf("Failed to soft delete software gateway: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	if result != nil {
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			logger.Errorf("Failed to get rows affected: %v", err)
			return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
		}

		if rowsAffected == 0 {
			return ErrSoftwareGatewayNotFound
		}
	}

	return nil
}

// Parses and validates the create and update software gateway request body
func parseCreatAndUpdateRequest(r *http.Request) (*CreateAndUpdateSoftwareGatewayRequest, error) {
	var req CreateAndUpdateSoftwareGatewayRequest
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Reject unexpected fields

	if err := decoder.Decode(&req); err != nil {
		logger.Infof("failed to parse create request: %v", err)
		if strings.Contains(err.Error(), "unknown field") {
			return &req, ErrUnexpectedFields
		}
		return &req, ErrInvalidRequestBody
	}

	// Validate the description is not empty
	if strings.TrimSpace(req.Description) == "" {
		return &req, ErrInvalidDescription
	}

	// Validate the organization id is not empty
	if req.OrganizationId == uuid.Nil {
		return &req, ErrInvalidOrganizationId
	}

	return &req, nil
}

// Validate OrganizationId exists
func validateOrganizationIdExists(pg connect.DatabaseExecutor, organizationId uuid.UUID) error {
	query := `
		SELECT 1 as exists FROM {{Organization}} WHERE Id = $1 AND IsDeleted = false`

	var result struct {
		Exists int `db:"exists"`
	}
	err := pg.QueryRowStruct(&result, query, organizationId)
	if err != nil {
		if err == sql.ErrNoRows {
			return ErrOrganizationNotFound
		}
		logger.Errorf("Failed to validate organization id: %v", err)
		return fmt.Errorf("%w: %v", ErrDatabaseOperation, err)
	}

	return nil
}

// Handler is the production-ready HTTP handler using default dependencies.
var (
	CreateHandler = CreateHandlerWithDeps(HandlerDeps{
		GetConnections:        connect.GetConnections,
		CreateSoftwareGateway: createSoftwareGateway,
	})
	GetAllHandler = GetAllHandlerWithDeps(HandlerDeps{
		GetConnections:         connect.GetConnections,
		GetAllSoftwareGateways: getAllSoftwareGateways,
	})
	GetByIdentifierHandler = GetByIdentifierHandlerWithDeps(HandlerDeps{
		GetConnections:     connect.GetConnections,
		GetSoftwareGateway: getSoftwareGatewayByIdentifier,
	})
	UpdateHandler = UpdateHandlerWithDeps(HandlerDeps{
		GetConnections:        connect.GetConnections,
		UpdateSoftwareGateway: updateSoftwareGateway,
	})
	DeleteHandler = DeleteHandlerWithDeps(HandlerDeps{
		GetConnections:        connect.GetConnections,
		DeleteSoftwareGateway: deleteSoftwareGateway,
	})
)
