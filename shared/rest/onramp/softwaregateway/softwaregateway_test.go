package softwaregateway

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test helper to create a mock request with JSON body
func createMockRequest(method, url string, body interface{}) *http.Request {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req := httptest.NewRequest(method, url, reqBody)
	req.Header.Set("Content-Type", "application/json")
	return req
}

// Test SoftwareGateway.ToResponse method
func Test_SoftwareGateway_ToResponse(t *testing.T) {
	t.Para<PERSON>l()

	// Create test software gateway
	now := time.Now().UTC()
	id := uuid.New()
	orgId := uuid.New()
	gateway := SoftwareGateway{
		Id:                    id,
		OrganizationId:        orgId,
		MachineKey:            "test-machine-key-123",
		APIKey:                "api-key-456",
		Token:                 "token-789",
		GatewayVersion:        nil,
		DateLastCheckedInUTC:  now,
		PushConfigOnNextCheck: true,
		IsEnabled:             true,
		Config:                `{"test": "config"}`,
		Name:                  "Test Gateway",
		Description:           "Test Software Gateway",
		CreatedAt:             now,
		UpdatedAt:             now.Add(time.Hour),
		TemplateId:            nil,
	}

	// Execute the method under test
	response := gateway.ToResponse()

	// Assert all fields are correctly mapped
	assert.Equal(t, gateway.Id, response.Id)
	assert.Equal(t, gateway.OrganizationId, response.OrganizationId)
	assert.Equal(t, gateway.MachineKey, response.MachineKey)
	assert.Equal(t, gateway.APIKey, response.APIKey)
	assert.Equal(t, gateway.Token, response.Token)
	assert.Equal(t, gateway.GatewayVersion, response.GatewayVersion)
	assert.Equal(t, gateway.DateLastCheckedInUTC, response.DateLastCheckedInUTC)
	assert.Equal(t, gateway.PushConfigOnNextCheck, response.PushConfigOnNextCheck)
	assert.Equal(t, gateway.IsEnabled, response.IsEnabled)
	assert.Equal(t, gateway.Config, response.Config)
	assert.Equal(t, gateway.Name, response.Name)
	assert.Equal(t, gateway.Description, response.Description)
	assert.Equal(t, gateway.CreatedAt, response.CreatedAt)
	assert.Equal(t, gateway.UpdatedAt, response.UpdatedAt)
	assert.Equal(t, gateway.TemplateId, response.TemplateId)
}

// Test parseCreatAndUpdateRequest function
func Test_parseCreatAndUpdateRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		requestBody interface{}
		expectedErr error
		wantErr     bool
		description string
		orgId       uuid.UUID
		enabled     bool
	}{
		{
			name: "valid request",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Valid software gateway description",
				OrganizationId: uuid.MustParse("f866ca63-7df8-432a-afe7-9f85252a0af3"),
				IsEnabled:      true,
			},
			expectedErr: nil,
			wantErr:     false,
			description: "Valid software gateway description",
			orgId:       uuid.MustParse("f866ca63-7df8-432a-afe7-9f85252a0af3"),
			enabled:     true,
		},
		{
			name: "empty description",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "",
			orgId:       uuid.New(),
			enabled:     true,
		},
		{
			name: "whitespace only description",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "   ",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "   ",
			orgId:       uuid.New(),
			enabled:     true,
		},
		{
			name: "description with leading/trailing spaces",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "  Valid description  ",
				OrganizationId: uuid.MustParse("7b47be08-b81d-4235-8104-e20f0f5946a4"),
				IsEnabled:      false,
			},
			expectedErr: nil,
			wantErr:     false,
			description: "  Valid description  ",
			orgId:       uuid.MustParse("7b47be08-b81d-4235-8104-e20f0f5946a4"),
			enabled:     false,
		},
		{
			name: "zero organization id",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Valid description",
				OrganizationId: uuid.Nil,
				IsEnabled:      true,
			},
			expectedErr: ErrInvalidOrganizationId,
			wantErr:     true,
			description: "Valid description",
			orgId:       uuid.Nil,
			enabled:     true,
		},
		{
			name: "negative organization id",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Valid description",
				OrganizationId: uuid.Nil,
				IsEnabled:      true,
			},
			expectedErr: ErrInvalidOrganizationId,
			wantErr:     true,
			description: "Valid description",
			orgId:       uuid.Nil,
			enabled:     true,
		},
		{
			name:        "invalid JSON",
			requestBody: "invalid json",
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
			description: "",
			orgId:       uuid.Nil,
			enabled:     false,
		},
		{
			name: "unexpected fields",
			requestBody: map[string]interface{}{
				"name":           "Test Gateway",
				"description":    "Valid description",
				"organizationid": "f866ca63-7df8-432a-afe7-9f85252a0af3",
				"isenabled":      true,
				"unknownfield":   "should cause error",
			},
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
			description: "Valid description",
			orgId:       uuid.MustParse("f866ca63-7df8-432a-afe7-9f85252a0af3"),
			enabled:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock request with test body
			req := createMockRequest("POST", "/software-gateways", tt.requestBody)

			// Execute the function under test
			result, err := parseCreatAndUpdateRequest(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.description, result.Description)
				assert.Equal(t, tt.orgId, result.OrganizationId)
				assert.Equal(t, tt.enabled, result.IsEnabled)
			}
		})
	}
}

// Test validateOrganizationIdExists function
func Test_validateOrganizationIdExists(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		organizationId uuid.UUID
		setupMock      func(*dbexecutor.FakeDBExecutor)
		expectedErr    error
		wantErr        bool
	}{
		{
			name:           "organization exists",
			organizationId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call for existing organization
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					result := dest.(*struct {
						Exists int `db:"exists"`
					})
					result.Exists = 1
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:           "organization does not exist",
			organizationId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock QueryRowStruct call returning sql.ErrNoRows for non-existent organization
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:           "database error",
			organizationId: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			err := validateOrganizationIdExists(mockDB, tt.organizationId)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test createSoftwareGateway function
func Test_createSoftwareGateway(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		request     *CreateAndUpdateSoftwareGatewayRequest
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name: "successful creation",
			request: &CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Test Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful software gateway creation
					gateway := dest.(*SoftwareGateway)
					gateway.Id = uuid.New()                      // Generated by the database
					gateway.OrganizationId = args[0].(uuid.UUID) // Use the OrganizationId from the request
					gateway.MachineKey = "test-gateway-123"
					gateway.Description = "Test Software Gateway"
					gateway.APIKey = "api-key-456"
					gateway.IsEnabled = true
					gateway.CreatedAt = time.Now().UTC()
					gateway.UpdatedAt = time.Now().UTC()
					gateway.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name: "database error",
			request: &CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Test Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := createSoftwareGateway(mockDB, tt.request)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Description, result.Description)
				assert.Equal(t, tt.request.OrganizationId, result.OrganizationId)
				assert.Equal(t, tt.request.IsEnabled, result.IsEnabled)
				assert.NotEmpty(t, result.MachineKey)
				assert.NotEmpty(t, result.APIKey)
			}
		})
	}
}

// Test getAllSoftwareGateways function
func Test_getAllSoftwareGateways(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
		expectCount int
	}{
		{
			name: "successful retrieval with multiple gateways",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful software gateways retrieval
					gateways := dest.(*[]SoftwareGateway)
					*gateways = []SoftwareGateway{
						{
							Id:             uuid.New(),
							OrganizationId: uuid.New(),
							MachineKey:     "gateway-1",
							Description:    "First Gateway",
							IsEnabled:      true,
						},
						{
							Id:             uuid.New(),
							OrganizationId: uuid.New(),
							MachineKey:     "gateway-2",
							Description:    "Second Gateway",
							IsEnabled:      false,
						},
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectCount: 2,
		},
		{
			name: "successful retrieval with empty result",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call with empty result
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate empty result
					gateways := dest.(*[]SoftwareGateway)
					*gateways = []SoftwareGateway{}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectCount: 0,
		},
		{
			name: "database error",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
			expectCount: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getAllSoftwareGateways(mockDB)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, *result, tt.expectCount)
			}
		})
	}
}

// Test getSoftwareGatewayByIdentifier function
func Test_getSoftwareGatewayByIdentifier(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  uuid.UUID
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful retrieval",
			identifier: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful software gateway retrieval
					gateway := dest.(*SoftwareGateway)
					gateway.Id = args[0].(uuid.UUID) // Use the identifier passed to the query
					gateway.OrganizationId = uuid.New()
					gateway.MachineKey = "test-gateway-123"
					gateway.Description = "Test Software Gateway"
					gateway.APIKey = "api-key-456"
					gateway.IsEnabled = true
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "software gateway not found",
			identifier: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock sql.ErrNoRows
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedErr: ErrSoftwareGatewayNotFound,
			wantErr:     true,
		},
		{
			name:       "database error",
			identifier: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getSoftwareGatewayByIdentifier(mockDB, tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.identifier, result.Id)
			}
		})
	}
}

// Test updateSoftwareGateway function
func Test_updateSoftwareGateway(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  uuid.UUID
		request     *CreateAndUpdateSoftwareGatewayRequest
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful update",
			identifier: uuid.New(),
			request: &CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      false,
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful software gateway update
					gateway := dest.(*SoftwareGateway)
					// The update function passes: Name, Description, OrganizationId, IsEnabled, UpdatedAt, identifier
					gateway.Id = args[5].(uuid.UUID)             // identifier (last argument)
					gateway.OrganizationId = args[2].(uuid.UUID) // OrganizationId (3rd argument)
					gateway.MachineKey = "test-gateway-123"
					gateway.Name = args[0].(string)        // Name (1st argument)
					gateway.Description = args[1].(string) // Description (2nd argument)
					gateway.IsEnabled = args[3].(bool)     // IsEnabled (4th argument)
					gateway.UpdatedAt = time.Now().UTC()
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "software gateway not found",
			identifier: uuid.New(),
			request: &CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock sql.ErrNoRows
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedErr: ErrSoftwareGatewayNotFound,
			wantErr:     true,
		},
		{
			name:       "database error",
			identifier: uuid.New(),
			request: &CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := updateSoftwareGateway(mockDB, tt.identifier, tt.request)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Description, result.Description)
				assert.Equal(t, tt.request.OrganizationId, result.OrganizationId)
				assert.Equal(t, tt.request.IsEnabled, result.IsEnabled)
				assert.Equal(t, tt.identifier, result.Id)
			}
		})
	}
}

// mockSQLResult implements sql.Result interface for testing
type mockSQLResult struct {
	rowsAffected int64
	lastInsertId int64
	err          error
}

func (m *mockSQLResult) LastInsertId() (int64, error) {
	return m.lastInsertId, m.err
}

func (m *mockSQLResult) RowsAffected() (int64, error) {
	return m.rowsAffected, m.err
}

// Test deleteSoftwareGateway function
func Test_deleteSoftwareGateway(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  uuid.UUID
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful deletion",
			identifier: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "software gateway not found - no rows affected",
			identifier: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock Exec call with zero rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrSoftwareGatewayNotFound,
			wantErr:     true,
		},
		{
			name:       "database error during execution",
			identifier: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error during Exec
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:       "error getting rows affected",
			identifier: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock error when getting rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 0, err: errors.New("failed to get rows affected")}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:       "nil result returned",
			identifier: uuid.New(),
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock nil result - should still succeed as result can be nil
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Setup mock database
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			err := deleteSoftwareGateway(mockDB, tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test CreateHandlerWithDeps function with working dependency injection
func Test_CreateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name               string
		requestBody        interface{}
		mockConnections    func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		mockCreateGateway  func(connect.DatabaseExecutor, *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error)
		setupOrgValidation func(*dbexecutor.FakeDBExecutor)
		expectedStatusCode int
		expectError        bool
	}{
		{
			name: "successful creation",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Test Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return true (organization exists)
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					result := dest.(*struct {
						Exists int `db:"exists"`
					})
					result.Exists = 1
					return nil
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockCreateGateway: func(pg connect.DatabaseExecutor, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return &SoftwareGateway{
					Id:             uuid.New(),
					OrganizationId: req.OrganizationId,
					MachineKey:     "test-gateway-123",
					Description:    req.Description,
					IsEnabled:      req.IsEnabled,
					CreatedAt:      time.Now().UTC(),
					UpdatedAt:      time.Now().UTC(),
				}, nil
			},
			setupOrgValidation: nil,
			expectedStatusCode: http.StatusOK,
			expectError:        false,
		},
		{
			name: "connections error",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Test Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			mockCreateGateway: func(pg connect.DatabaseExecutor, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			setupOrgValidation: nil,
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
		{
			name:        "invalid request body",
			requestBody: "invalid json",
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockCreateGateway: func(pg connect.DatabaseExecutor, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			setupOrgValidation: nil,
			expectedStatusCode: http.StatusBadRequest,
			expectError:        true,
		},
		{
			name: "organization not found",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Test Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return false (organization does not exist)
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockCreateGateway: func(pg connect.DatabaseExecutor, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			setupOrgValidation: nil,
			expectedStatusCode: http.StatusNotFound,
			expectError:        true,
		},
		{
			name: "organization validation database error",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Test Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockCreateGateway: func(pg connect.DatabaseExecutor, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			setupOrgValidation: nil,
			expectedStatusCode: http.StatusBadRequest,
			expectError:        true,
		},
		{
			name: "create gateway error",
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Test Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return true (organization exists)
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					result := dest.(*struct {
						Exists int `db:"exists"`
					})
					result.Exists = 1
					return nil
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockCreateGateway: func(pg connect.DatabaseExecutor, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, ErrDatabaseOperation
			},
			setupOrgValidation: nil,
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mock dependencies
			deps := HandlerDeps{
				GetConnections:        tt.mockConnections,
				CreateSoftwareGateway: tt.mockCreateGateway,
			}
			handler := CreateHandlerWithDeps(deps)

			// Create request
			req := createMockRequest("POST", "/software-gateways", tt.requestBody)
			rr := httptest.NewRecorder()

			// Execute handler
			handler(rr, req)

			// Assert response
			assert.Equal(t, tt.expectedStatusCode, rr.Code)

			if !tt.expectError && rr.Code == http.StatusOK {
				var responseWrapper struct {
					Status  string                  `json:"status"`
					Data    SoftwareGatewayResponse `json:"data"`
					Message string                  `json:"message"`
					Code    int                     `json:"code"`
				}
				err := json.Unmarshal(rr.Body.Bytes(), &responseWrapper)
				assert.NoError(t, err)
				assert.Equal(t, "success", responseWrapper.Status)
				assert.NotEmpty(t, responseWrapper.Data.Id)
			}
		})
	}
}

// Test GetAllHandlerWithDeps function with working dependency injection
func Test_GetAllHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name               string
		mockConnections    func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		mockGetAllGateways func(connect.DatabaseExecutor) (*[]SoftwareGateway, error)
		expectedStatusCode int
		expectError        bool
		expectedCount      int
	}{
		{
			name: "successful retrieval with multiple gateways",
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetAllGateways: func(pg connect.DatabaseExecutor) (*[]SoftwareGateway, error) {
				gateways := []SoftwareGateway{
					{
						Id:             uuid.New(),
						OrganizationId: uuid.New(),
						MachineKey:     "gateway-1",
						Description:    "First Gateway",
						IsEnabled:      true,
					},
					{
						Id:             uuid.New(),
						OrganizationId: uuid.New(),
						MachineKey:     "gateway-2",
						Description:    "Second Gateway",
						IsEnabled:      false,
					},
				}
				return &gateways, nil
			},
			expectedStatusCode: http.StatusOK,
			expectError:        false,
			expectedCount:      2,
		},
		{
			name: "successful retrieval with empty result",
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetAllGateways: func(pg connect.DatabaseExecutor) (*[]SoftwareGateway, error) {
				gateways := []SoftwareGateway{}
				return &gateways, nil
			},
			expectedStatusCode: http.StatusOK,
			expectError:        false,
			expectedCount:      0,
		},
		{
			name: "connections error",
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			mockGetAllGateways: func(pg connect.DatabaseExecutor) (*[]SoftwareGateway, error) {
				return nil, nil
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
			expectedCount:      0,
		},
		{
			name: "get all gateways error",
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetAllGateways: func(pg connect.DatabaseExecutor) (*[]SoftwareGateway, error) {
				return nil, ErrDatabaseOperation
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
			expectedCount:      0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mock dependencies
			deps := HandlerDeps{
				GetConnections:         tt.mockConnections,
				GetAllSoftwareGateways: tt.mockGetAllGateways,
			}
			handler := GetAllHandlerWithDeps(deps)

			// Create request
			req := httptest.NewRequest("GET", "/software-gateways", nil)
			rr := httptest.NewRecorder()

			// Execute handler
			handler(rr, req)

			// Assert response
			assert.Equal(t, tt.expectedStatusCode, rr.Code)

			if !tt.expectError && rr.Code == http.StatusOK {
				var responseWrapper struct {
					Status  string                    `json:"status"`
					Data    []SoftwareGatewayResponse `json:"data"`
					Message string                    `json:"message"`
					Code    int                       `json:"code"`
				}
				err := json.Unmarshal(rr.Body.Bytes(), &responseWrapper)
				assert.NoError(t, err)
				assert.Equal(t, "success", responseWrapper.Status)
				assert.Len(t, responseWrapper.Data, tt.expectedCount)
			}
		})
	}
}

// Test GetByIdentifierHandlerWithDeps function
func Test_GetByIdentifierHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name               string
		identifier         uuid.UUID
		mockConnections    func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		mockGetGateway     func(connect.DatabaseExecutor, uuid.UUID) (*SoftwareGateway, error)
		expectedStatusCode int
		expectError        bool
	}{
		{
			name:       "successful retrieval",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error) {
				return &SoftwareGateway{
					Id:                    identifier, // Return the same ID that was passed in
					OrganizationId:        uuid.New(),
					MachineKey:            identifier.String(),
					APIKey:                "test-api-key",
					Token:                 "test-token",
					GatewayVersion:        nil,
					DateLastCheckedInUTC:  time.Now().UTC(),
					PushConfigOnNextCheck: false,
					IsEnabled:             true,
					Config:                `{"test": "config"}`,
					Name:                  "Test Gateway",
					Description:           "Test Software Gateway",
					CreatedAt:             time.Now().UTC(),
					UpdatedAt:             time.Now().UTC(),
					TemplateId:            nil,
				}, nil
			},
			expectedStatusCode: http.StatusOK,
			expectError:        false,
		},
		{
			name:       "invalid identifier",
			identifier: uuid.Nil,
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error) {
				return nil, ErrSoftwareGatewayNotFound
			},
			expectedStatusCode: http.StatusNotFound,
			expectError:        true,
		},
		{
			name:       "invalid uuid format",
			identifier: uuid.Nil, // This will be overridden with invalid string
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error) {
				return nil, nil
			},
			expectedStatusCode: http.StatusBadRequest,
			expectError:        true,
		},
		{
			name:       "nil software gateway returned",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error) {
				return nil, nil // Returns nil without error
			},
			expectedStatusCode: http.StatusNotFound,
			expectError:        true,
		},
		{
			name:       "software gateway not found",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error) {
				return nil, ErrSoftwareGatewayNotFound
			},
			expectedStatusCode: http.StatusNotFound,
			expectError:        true,
		},
		{
			name:       "connections error",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			mockGetGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error) {
				return nil, nil
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
		{
			name:       "database error",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockGetGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) (*SoftwareGateway, error) {
				return nil, ErrDatabaseOperation
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mock dependencies
			deps := HandlerDeps{
				GetConnections:     tt.mockConnections,
				GetSoftwareGateway: tt.mockGetGateway,
			}
			handler := GetByIdentifierHandlerWithDeps(deps)

			// Create request with mux vars
			var identifierStr string
			if tt.name == "invalid uuid format" {
				identifierStr = "invalid-uuid-string"
			} else {
				identifierStr = tt.identifier.String()
			}
			req := httptest.NewRequest("GET", "/software-gateways/"+identifierStr, nil)
			req = mux.SetURLVars(req, map[string]string{"identifier": identifierStr})
			rr := httptest.NewRecorder()

			// Execute handler
			handler(rr, req)

			// Assert response
			assert.Equal(t, tt.expectedStatusCode, rr.Code)

			if !tt.expectError && rr.Code == http.StatusOK {
				var responseWrapper struct {
					Status  string                  `json:"status"`
					Data    SoftwareGatewayResponse `json:"data"`
					Message string                  `json:"message"`
					Code    int                     `json:"code"`
				}
				err := json.Unmarshal(rr.Body.Bytes(), &responseWrapper)
				assert.NoError(t, err)
				assert.Equal(t, "success", responseWrapper.Status)
				assert.Equal(t, tt.identifier, responseWrapper.Data.Id)
			}
		})
	}
}

// Test UpdateHandlerWithDeps function
func Test_UpdateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name               string
		identifier         uuid.UUID
		requestBody        interface{}
		mockConnections    func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		mockUpdateGateway  func(connect.DatabaseExecutor, uuid.UUID, *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error)
		expectedStatusCode int
		expectError        bool
	}{
		{
			name:       "successful update",
			identifier: uuid.New(),
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Updated Gateway",
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      false,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return true (organization exists)
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					result := dest.(*struct {
						Exists int `db:"exists"`
					})
					result.Exists = 1
					return nil
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return &SoftwareGateway{
					Id:             identifier, // Return the same ID that was passed in
					OrganizationId: req.OrganizationId,
					MachineKey:     identifier.String(),
					Name:           req.Name,
					Description:    req.Description,
					IsEnabled:      req.IsEnabled,
					UpdatedAt:      time.Now().UTC(),
				}, nil
			},
			expectedStatusCode: http.StatusOK,
			expectError:        false,
		},
		{
			name:       "invalid identifier",
			identifier: uuid.Nil,
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, ErrSoftwareGatewayNotFound
			},
			expectedStatusCode: http.StatusNotFound,
			expectError:        true,
		},
		{
			name:       "invalid uuid format",
			identifier: uuid.Nil, // This will be overridden with invalid string
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			expectedStatusCode: http.StatusBadRequest,
			expectError:        true,
		},
		{
			name:       "nil software gateway returned after update",
			identifier: uuid.New(),
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Name:           "Test Gateway",
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return true (organization exists)
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					result := dest.(*struct {
						Exists int `db:"exists"`
					})
					result.Exists = 1
					return nil
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil // Returns nil without error
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
		{
			name:        "invalid request body",
			identifier:  uuid.New(),
			requestBody: "invalid json",
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			expectedStatusCode: http.StatusBadRequest,
			expectError:        true,
		},
		{
			name:       "connections error",
			identifier: uuid.New(),
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
		{
			name:       "organization validation database error",
			identifier: uuid.New(),
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			expectedStatusCode: http.StatusBadRequest,
			expectError:        true,
		},
		{
			name:       "organization not found",
			identifier: uuid.New(),
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return false (organization does not exist)
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, nil
			},
			expectedStatusCode: http.StatusNotFound,
			expectError:        true,
		},
		{
			name:       "update gateway error",
			identifier: uuid.New(),
			requestBody: CreateAndUpdateSoftwareGatewayRequest{
				Description:    "Updated Software Gateway",
				OrganizationId: uuid.New(),
				IsEnabled:      true,
			},
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				mockDB := &dbexecutor.FakeDBExecutor{}
				// Mock organization validation to return true (organization exists)
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					result := dest.(*struct {
						Exists int `db:"exists"`
					})
					result.Exists = 1
					return nil
				}
				return &connect.Connections{
					Postgres: mockDB,
				}, nil
			},
			mockUpdateGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID, req *CreateAndUpdateSoftwareGatewayRequest) (*SoftwareGateway, error) {
				return nil, ErrDatabaseOperation
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mock dependencies
			deps := HandlerDeps{
				GetConnections:        tt.mockConnections,
				UpdateSoftwareGateway: tt.mockUpdateGateway,
			}
			handler := UpdateHandlerWithDeps(deps)

			// Create request with mux vars
			var identifierStr string
			if tt.name == "invalid uuid format" {
				identifierStr = "invalid-uuid-string"
			} else {
				identifierStr = tt.identifier.String()
			}
			req := createMockRequest("PUT", "/software-gateways/"+identifierStr, tt.requestBody)
			req = mux.SetURLVars(req, map[string]string{"identifier": identifierStr})
			rr := httptest.NewRecorder()

			// Execute handler
			handler(rr, req)

			// Assert response
			assert.Equal(t, tt.expectedStatusCode, rr.Code)

			if !tt.expectError && rr.Code == http.StatusOK {
				var responseWrapper struct {
					Status  string                  `json:"status"`
					Data    SoftwareGatewayResponse `json:"data"`
					Message string                  `json:"message"`
					Code    int                     `json:"code"`
				}
				err := json.Unmarshal(rr.Body.Bytes(), &responseWrapper)
				assert.NoError(t, err)
				assert.Equal(t, "success", responseWrapper.Status)
				assert.Equal(t, tt.identifier, responseWrapper.Data.Id)
			}
		})
	}
}

// Test DeleteHandlerWithDeps function
func Test_DeleteHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name               string
		identifier         uuid.UUID
		mockConnections    func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
		mockDeleteGateway  func(connect.DatabaseExecutor, uuid.UUID) error
		expectedStatusCode int
		expectError        bool
	}{
		{
			name:       "successful deletion",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) error {
				return nil
			},
			expectedStatusCode: http.StatusOK,
			expectError:        false,
		},
		{
			name:       "invalid identifier",
			identifier: uuid.Nil,
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) error {
				return ErrSoftwareGatewayNotFound
			},
			expectedStatusCode: http.StatusNotFound,
			expectError:        true,
		},
		{
			name:       "invalid uuid format",
			identifier: uuid.Nil, // This will be overridden with invalid string
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) error {
				return nil
			},
			expectedStatusCode: http.StatusBadRequest,
			expectError:        true,
		},
		{
			name:       "software gateway not found",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) error {
				return ErrSoftwareGatewayNotFound
			},
			expectedStatusCode: http.StatusNotFound,
			expectError:        true,
		},
		{
			name:       "connections error",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return nil, errors.New("connection failed")
			},
			mockDeleteGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) error {
				return nil
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
		{
			name:       "delete gateway error",
			identifier: uuid.New(),
			mockConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
				return &connect.Connections{
					Postgres: &dbexecutor.FakeDBExecutor{},
				}, nil
			},
			mockDeleteGateway: func(pg connect.DatabaseExecutor, identifier uuid.UUID) error {
				return ErrDatabaseOperation
			},
			expectedStatusCode: http.StatusInternalServerError,
			expectError:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with mock dependencies
			deps := HandlerDeps{
				GetConnections:        tt.mockConnections,
				DeleteSoftwareGateway: tt.mockDeleteGateway,
			}
			handler := DeleteHandlerWithDeps(deps)

			// Create request with mux vars
			var identifierStr string
			if tt.name == "invalid uuid format" {
				identifierStr = "invalid-uuid-string"
			} else {
				identifierStr = tt.identifier.String()
			}
			req := httptest.NewRequest("DELETE", "/software-gateways/"+identifierStr, nil)
			req = mux.SetURLVars(req, map[string]string{"identifier": identifierStr})
			rr := httptest.NewRecorder()

			// Execute handler
			handler(rr, req)

			// Assert response
			assert.Equal(t, tt.expectedStatusCode, rr.Code)

			if !tt.expectError && rr.Code == http.StatusOK {
				var responseWrapper struct {
					Status  string      `json:"status"`
					Data    interface{} `json:"data"`
					Message string      `json:"message"`
					Code    int         `json:"code"`
				}
				err := json.Unmarshal(rr.Body.Bytes(), &responseWrapper)
				assert.NoError(t, err)
				assert.Equal(t, "success", responseWrapper.Status)
				assert.Nil(t, responseWrapper.Data)
			}
		})
	}
}
