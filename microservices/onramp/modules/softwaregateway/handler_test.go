package softwaregateway

import (
	"net/http"
	"testing"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
)

func TestNewHandler(t *testing.T) {
	handler := NewHandler()

	assert.NotNil(t, handler, "NewHandler should return a non-nil handler")
	assert.IsType(t, &Hand<PERSON>{}, handler, "NewHandler should return a *Handler")
}

func TestHandler_RegisterRoutes(t *testing.T) {
	handler := NewHandler()
	router := mux.NewRouter()

	// Register routes
	handler.RegisterRoutes(router)

	// Test that routes are properly registered by walking through them
	var routes []string
	var methods []string

	err := router.Walk(func(route *mux.Route, router *mux.Router, ancestors []*mux.Route) error {
		template, _ := route.GetPathTemplate()
		methodsSlice, _ := route.GetMethods()

		routes = append(routes, template)
		if len(methodsSlice) > 0 {
			methods = append(methods, methodsSlice[0])
		}
		return nil
	})

	assert.NoError(t, err, "Walking routes should not produce an error")

	// Verify expected routes are registered
	expectedRoutes := []string{
		// Software Gateway CRUD
		"/softwaregateway",
		"/softwaregateway",
		"/softwaregateway/{identifier}",
		"/softwaregateway/{identifier}",
		"/softwaregateway/{identifier}",
		// Software Gateway Config (Get with resolved template settings)
		"/softwaregateway/{identifier}/config",
		// Legacy support: Bulk upsert overrides without organizationId in URL path
		"/softwaregateway/{identifier}/config",
		// Template Management
		"/organization/{organizationId}/softwaregatewayconfig/templates",
		"/organization/{organizationId}/softwaregatewayconfig/templates",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}",
		// Template Settings Management
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings",
		"/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings/{setting}",
		// Gateway Setting Overrides Management
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides",
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides",
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides",
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/bulk",
		"/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/{setting}",
		// Base Settings Management
		"/softwaregatewayconfig/basesettings",
		"/softwaregatewayconfig/basesettings",
		"/softwaregatewayconfig/basesettings/{setting}",
		"/softwaregatewayconfig/basesettings/{setting}",
		"/softwaregatewayconfig/basesettings/{setting}",
	}

	expectedMethods := []string{
		// Software Gateway CRUD
		http.MethodPost,
		http.MethodGet,
		http.MethodGet,
		http.MethodPatch,
		http.MethodDelete,
		// Software Gateway Config
		http.MethodGet,
		// Legacy support: Bulk upsert overrides without organizationId in URL path
		http.MethodPatch,
		// Template Management
		http.MethodPost,
		http.MethodGet,
		http.MethodGet,
		http.MethodPatch,
		http.MethodDelete,
		// Template Settings Management
		http.MethodPost,
		http.MethodGet,
		http.MethodPut,
		http.MethodDelete,
		// Gateway Setting Overrides Management
		http.MethodPost,
		http.MethodGet,
		http.MethodPut,
		http.MethodPost,
		http.MethodDelete,
		// Base Settings Management
		http.MethodPost,
		http.MethodGet,
		http.MethodGet,
		http.MethodPatch,
		http.MethodDelete,
	}

	assert.Equal(t, len(expectedRoutes), len(routes), "Should register correct number of routes")
	assert.Equal(t, len(expectedMethods), len(methods), "Should register correct number of methods")

	// Check that all expected routes and methods are present
	for i, expectedRoute := range expectedRoutes {
		assert.Contains(t, routes, expectedRoute, "Route %s should be registered", expectedRoute)
		if i < len(methods) {
			assert.Contains(t, methods, expectedMethods[i], "Method %s should be registered", expectedMethods[i])
		}
	}
}

func TestHandler_RegisterRoutes_RouterIntegration(t *testing.T) {
	handler := NewHandler()
	router := mux.NewRouter()

	// Register routes
	handler.RegisterRoutes(router)

	// Test specific route patterns by creating test requests
	testCases := []struct {
		method      string
		path        string
		shouldMatch bool
	}{
		{http.MethodPost, "/softwaregateway", true},
		{http.MethodGet, "/softwaregateway", true},
		{http.MethodGet, "/softwaregateway/test-gateway", true},
		{http.MethodPatch, "/softwaregateway/test-gateway", true},
		{http.MethodDelete, "/softwaregateway/test-gateway", true},
		{http.MethodGet, "/softwaregateway/test-gateway/config", true},
		{http.MethodPatch, "/softwaregateway/test-gateway/config", true}, // Legacy PATCH config route for bulk upsert overrides
		{http.MethodPut, "/softwaregateway", false},                      // Not registered
		{http.MethodGet, "/softwaregateway/test-gateway/invalid", false}, // Wrong path
		{http.MethodPost, "/softwaregateway/test-gateway/config", false}, // Wrong method for config
	}

	for _, tc := range testCases {
		t.Run(tc.method+"_"+tc.path, func(t *testing.T) {
			req, err := http.NewRequest(tc.method, tc.path, nil)
			assert.NoError(t, err)

			var match mux.RouteMatch
			matched := router.Match(req, &match)

			if tc.shouldMatch {
				assert.True(t, matched, "Route %s %s should match", tc.method, tc.path)
			} else {
				assert.False(t, matched, "Route %s %s should not match", tc.method, tc.path)
			}
		})
	}
}

func TestHandler_RegisterRoutes_ConfigRoutes(t *testing.T) {
	handler := NewHandler()
	router := mux.NewRouter()

	// Register routes
	handler.RegisterRoutes(router)

	// Specifically test config-related routes
	configTestCases := []struct {
		name     string
		method   string
		path     string
		expected bool
	}{
		{
			name:     "GET config route",
			method:   http.MethodGet,
			path:     "/softwaregateway/my-gateway/config",
			expected: true,
		},
		{
			name:     "PATCH config route - legacy support for bulk upsert overrides",
			method:   http.MethodPatch,
			path:     "/softwaregateway/my-gateway/config",
			expected: true,
		},
		{
			name:     "POST config route - not supported",
			method:   http.MethodPost,
			path:     "/softwaregateway/my-gateway/config",
			expected: false,
		},
		{
			name:     "DELETE config route - not supported",
			method:   http.MethodDelete,
			path:     "/softwaregateway/my-gateway/config",
			expected: false,
		},
	}

	for _, tc := range configTestCases {
		t.Run(tc.name, func(t *testing.T) {
			req, err := http.NewRequest(tc.method, tc.path, nil)
			assert.NoError(t, err)

			var match mux.RouteMatch
			matched := router.Match(req, &match)

			assert.Equal(t, tc.expected, matched, "Route matching should be %v for %s %s", tc.expected, tc.method, tc.path)
		})
	}
}

func TestHandler_RegisterRoutes_BaseSettingsRoutes(t *testing.T) {
	handler := NewHandler()
	router := mux.NewRouter()

	// Register routes
	handler.RegisterRoutes(router)

	// Test base settings routes
	baseSettingsTestCases := []struct {
		name     string
		method   string
		path     string
		expected bool
	}{
		{
			name:     "POST base settings route",
			method:   http.MethodPost,
			path:     "/softwaregatewayconfig/basesettings",
			expected: true,
		},
		{
			name:     "GET list base settings route",
			method:   http.MethodGet,
			path:     "/softwaregatewayconfig/basesettings",
			expected: true,
		},
		{
			name:     "GET specific base setting route",
			method:   http.MethodGet,
			path:     "/softwaregatewayconfig/basesettings/log_level",
			expected: true,
		},
		{
			name:     "PATCH specific base setting route",
			method:   http.MethodPatch,
			path:     "/softwaregatewayconfig/basesettings/log_level",
			expected: true,
		},
		{
			name:     "DELETE specific base setting route",
			method:   http.MethodDelete,
			path:     "/softwaregatewayconfig/basesettings/log_level",
			expected: true,
		},
		{
			name:     "PUT base settings route - not supported",
			method:   http.MethodPut,
			path:     "/softwaregatewayconfig/basesettings",
			expected: false,
		},
	}

	for _, tc := range baseSettingsTestCases {
		t.Run(tc.name, func(t *testing.T) {
			req, err := http.NewRequest(tc.method, tc.path, nil)
			assert.NoError(t, err)

			var match mux.RouteMatch
			matched := router.Match(req, &match)

			assert.Equal(t, tc.expected, matched, "Route matching should be %v for %s %s", tc.expected, tc.method, tc.path)
		})
	}
}
