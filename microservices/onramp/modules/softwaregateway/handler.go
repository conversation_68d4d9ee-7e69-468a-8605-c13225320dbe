package softwaregateway

import (
	"net/http"

	"synapse-its.com/shared/logger"
	RestSoftwareGateway "synapse-its.com/shared/rest/onramp/softwaregateway"
	RestSoftwareGatewayConfig "synapse-its.com/shared/rest/onramp/softwaregateway/config"

	"github.com/gorilla/mux"
)

type Handler struct{}

func NewHandler() *Handler {
	return &Handler{}
}

func (h *Handler) RegisterRoutes(router *mux.Router) {
	logger.Info("Registering software gateway routes")
	// TODO prepend Organization in these endpoints
	router.HandleFunc("/softwaregateway", RestSoftwareGateway.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/softwaregateway", RestSoftwareGateway.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregateway/{identifier}", RestSoftwareGateway.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregateway/{identifier}", RestSoftwareGateway.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/softwaregateway/{identifier}", RestSoftwareGateway.DeleteHandler).Methods(http.MethodDelete)

	// Software Gateway Config (Get with resolved template settings)
	// TODO prepend Organization in these endpoints
	router.HandleFunc("/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.GetByIdentifierHandler).Methods(http.MethodGet)

	// TODO: Remove this legacy endpoint when GUI is updated to include organizationId in URL
	// Legacy support: Bulk upsert overrides without organizationId in URL path
	router.HandleFunc("/softwaregateway/{identifier}/config", RestSoftwareGatewayConfig.BulkUpsertOverridesLegacyHandler).Methods(http.MethodPatch)

	// Template Management
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates", RestSoftwareGatewayConfig.CreateTemplateHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates", RestSoftwareGatewayConfig.ListTemplatesHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}", RestSoftwareGatewayConfig.GetTemplateHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}", RestSoftwareGatewayConfig.UpdateTemplateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}", RestSoftwareGatewayConfig.DeleteTemplateHandler).Methods(http.MethodDelete)

	// Template Settings Management
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings", RestSoftwareGatewayConfig.CreateOrUpdateTemplateSettingHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings", RestSoftwareGatewayConfig.GetTemplateSettingsHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings", RestSoftwareGatewayConfig.BulkReplaceTemplateSettingsHandler).Methods(http.MethodPut)
	router.HandleFunc("/organization/{organizationId}/softwaregatewayconfig/templates/{templateId}/settings/{setting}", RestSoftwareGatewayConfig.DeleteTemplateSettingHandler).Methods(http.MethodDelete)

	// Gateway Setting Overrides Management
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides", RestSoftwareGatewayConfig.CreateOrUpdateOverrideHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides", RestSoftwareGatewayConfig.GetOverridesHandler).Methods(http.MethodGet)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides", RestSoftwareGatewayConfig.BulkReplaceOverridesHandler).Methods(http.MethodPut)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/bulk", RestSoftwareGatewayConfig.BulkUpsertOverridesHandler).Methods(http.MethodPost)
	router.HandleFunc("/organization/{organizationId}/softwaregateway/{identifier}/config/overrides/{setting}", RestSoftwareGatewayConfig.DeleteOverrideHandler).Methods(http.MethodDelete)

	// Base Settings Management
	// This is synapse only, we only want synapse super admins to be able to use these endpoints
	router.HandleFunc("/softwaregatewayconfig/basesettings", RestSoftwareGatewayConfig.CreateBaseSettingHandler).Methods(http.MethodPost)
	router.HandleFunc("/softwaregatewayconfig/basesettings", RestSoftwareGatewayConfig.ListBaseSettingsHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregatewayconfig/basesettings/{setting}", RestSoftwareGatewayConfig.GetBaseSettingHandler).Methods(http.MethodGet)
	router.HandleFunc("/softwaregatewayconfig/basesettings/{setting}", RestSoftwareGatewayConfig.UpdateBaseSettingHandler).Methods(http.MethodPatch)
	router.HandleFunc("/softwaregatewayconfig/basesettings/{setting}", RestSoftwareGatewayConfig.DeleteBaseSettingHandler).Methods(http.MethodDelete)
}
