import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { forkJoin } from 'rxjs';
import { Organization, OrganizationsService } from '../../core/services/organization.service';
import { AuthService } from '../../core/services/auth.service';
interface ExtendedOrganization extends Organization {
  hasDeviceGroupScope: boolean;
  hasLocationGroupScope: boolean;
  hasOrgManageDevices: boolean;
}
@Component({
  selector: 'app-menu',
  standalone: false,
  templateUrl: './menu.component.html',
  styleUrl: './menu.component.css'
})
export class MenuComponent implements OnInit {
  selectedKey: string | null = null;
  isCollapsed = false;
  openMap: { [name: string]: boolean } = {
    sub1: false,
    sub2: false,
    sub3: false,
    sub4: false
  };
  isFavorite = false;
  hasSynapseScope = false;
  organizationsMenu: ExtendedOrganization[] = [];

  constructor(
    private router: Router,
    private organizationsService: OrganizationsService,
    private authService: AuthService,
    private message: NzMessageService
  ) { }

  ngOnInit() {
    this.router.events.subscribe(() => {
      this.checkActiveMenu();
    });
    this.loadPermissions();
  }

  private loadPermissions() {
    this.authService.permissions$.subscribe({
      next: (response: any) => {
        if (!response || !Array.isArray(response.data.permissions)) {
          this.hasSynapseScope = false;
          this.organizationsMenu = [];
          return;
        }
        const permissions = response.data.permissions;
        this.hasSynapseScope = permissions.some(
          (perm: any) => perm.scope === 'synapse' && perm.organizationId === '55d832bf-cd0d-5c8d-b06c-c0f73ae8b7cd'
        );

        const orgIds = [...new Set(permissions.map((perm: any) => perm.organizationId))] as string[];
        if (orgIds.length === 0) {
          this.organizationsMenu = [];
          return;
        }

        // Save permissions by organizationId
        const orgPermissions = permissions.reduce((acc: { [key: string]: any[] }, perm: any) => {
          if (!acc[perm.organizationId]) {
            acc[perm.organizationId] = [];
          }
          acc[perm.organizationId].push(perm);
          return acc;
        }, {});
        console.log('Permissions by organization:', orgPermissions);

        const orgRequests = orgIds.map((orgId: string) =>
          this.organizationsService.getOrganizationsId(orgId)
        );

        forkJoin(orgRequests).subscribe({
          next: (orgs: any) => {
            this.organizationsMenu = orgs
              .filter((org: any): org is Organization => org != null && !!org.id && !!org.name)
              .map((org: any) => ({
                ...org,
                hasDeviceGroupScope: (orgPermissions[org.id] || []).some(
                  (perm: any) => perm.scope === 'device_group'
                ),
                hasLocationGroupScope: (orgPermissions[org.id] || []).some(
                  (perm: any) => perm.scope === 'location_group'
                ),
                // used to show the display of the Device menu item in the future
                // hasOrgManageDevices: (orgPermissions[org.id] || []).some(
                //   (perm: any) => perm.scope === 'org' && perm.permissions.includes('org_manage_devices')
                // )
              }))
              .sort((a: any, b: any) => a.name.localeCompare(b.name));
            console.log('Loaded organizationsMenu:', this.organizationsMenu);
          },
          error: (err) => {
            console.error('Error fetching organizationsMenu:', err);
            this.message.create('error', 'Failed to load organizationsMenu. Please try again.', { nzDuration: 5000 });
            this.organizationsMenu = [];
          }
        });
      },
      error: (err) => {
        this.hasSynapseScope = false;
        this.organizationsMenu = [];
      }
    });
    // const storedPermissions = localStorage.getItem('permissions');
    // this.authService.permissions$.subscribe((permissions) => {
    //   if (!permissions && storedPermissions) {
    //     this.authService.getUserPermissions().subscribe();
    //   }
    // }).unsubscribe();
  }

  openHandler(key: string, open?: boolean): void {
    if (open !== undefined) {
      this.openMap[key] = open;
    } else {
      this.openMap[key] = true;
    }
  }

  private checkActiveMenu(): void {
    const activeRoutes = ['/permissions', '/users'];
    if (activeRoutes.some((route) => this.isActive(route))) {
      this.openMap['sub1'] = true;
      this.openHandler('sub1');
    } else {
      this.openMap['sub1'] = false;
    }
  }

  toggleCollapsed(): void {
    this.isCollapsed = !this.isCollapsed;
  }

  isActive(route: string): boolean {
    return this.router.url.includes(route);
  }

  handleFavorite() {
    this.isFavorite = !this.isFavorite;
  }
}