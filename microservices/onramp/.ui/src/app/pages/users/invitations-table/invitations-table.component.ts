import { Component, Input } from '@angular/core';
import { Invitations } from '../../../core/models/users.model';
import { NzModalRef, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { UsersService } from '../../../core/services/users.service';

@Component({
  selector: 'app-invitations-table',
  standalone: false,
  templateUrl: './invitations-table.component.html',
  styleUrl: './invitations-table.component.css'
})
export class InvitationsTableComponent {
  @Input() orgId: any[] = [];
  @Input() listDataInvitations: any[] = [];
  @Input() isLoadingTable = false;
  highlightedRowId: string | null = null;
  isLoadingResend: boolean[] = [];
  confirmModal?: NzModalRef;
  constructor(
    private modalService: NzModalService,
    private message: NzMessageService,
  ) { }

  ngOnInit() { }
  capitalize(value: string): string {
    if (!value) return value;
    return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
  }
  handleResend(value: any, index: any) {
    this.isLoadingResend[index] = true;
    setTimeout(() => {
      this.message.create('success', 'Message has been sent successfully!');
      this.isLoadingResend[index] = false;
    }, 3000);
  }
  handleDelete(value: any) {
    this.confirmModal = this.modalService.confirm({
      nzTitle: `<b>Delete Confirmation</b>`,
      nzContent: `Are you sure you want to delete the <b>${value.email}</b>?`,
      nzClassName: 'custom-confirm-modal delete-modal-invitations',
      nzWidth: '500px',
      nzOnOk: () => {
        console.log('click ok delete');

      }
    });
  }
}
