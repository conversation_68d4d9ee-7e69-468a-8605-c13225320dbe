<nz-modal class="modal-create-invite" [(nzVisible)]="isVisible" [nzTitle]="modalTitle" (nzOnCancel)="handleCancel()"
  [nzOkText]="'Save'" [nzFooter]="modalFooter" nzWidth="440px">
  <ng-template #modalTitle>
    <div class="content-header">
      <h2 style="margin-bottom: 0;">{{ isEditMode ? 'Edit User' : 'Invite User' }}</h2>
      <span>This will create a new Role based on the specified
        template.</span>
    </div>
  </ng-template>
  <ng-container *nzModalContent>
    <nz-form [formGroup]="form" class="modal-add-invite">
      <nz-form-item>
        <nz-form-control nzErrorTip="Please Input E-mail.">
          <label>E-mail</label>
          <nz-input-group nzErrorTip="Please Input E-mail.">
            <input class="h-4 br-8" nz-input id="input-user-email" formControlName="email" placeholder="Enter E-mail" />
          </nz-input-group>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control>
          <label>Organization Role</label>
          <nz-form-control nzErrorTip="Please select a Organization Role!">
            <nz-select class="select-template" formControlName="organizationrole"
              nzPlaceHolder="Select a Organization Role" [nzOptions]="listOrgRole"></nz-select>
          </nz-form-control>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control>
          <label>Message (Optional)</label>
          <nz-textarea-count [nzMaxCharacterCount]="2000">
            <textarea class="br-8" id="input-user-message" formControlName="message" nz-input rows="4"
              placeholder="Enter Role Message"></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>
    </nz-form>
  </ng-container>
  <ng-template #modalFooter>
    <button id="btn-create-invite" class="btn-submit-invite h-4 br-8" nz-button nzType="primary" (click)="handleSave()">
      Send Invitation
    </button>
  </ng-template>
</nz-modal>