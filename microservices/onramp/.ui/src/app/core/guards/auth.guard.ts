import { Injectable } from '@angular/core';
import {
  CanActivate, Router,
  ActivatedRouteSnapshot, RouterStateSnapshot
} from '@angular/router';
import { AuthService, UserProfile } from '../services/auth.service';
import { interval, Observable, of } from 'rxjs';
import { map, catchError, switchMap, take } from 'rxjs/operators';
import { NzModalService } from 'ng-zorro-antd/modal';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  private isLocalStorageValid: string | null = null;
  constructor(
    private auth: AuthService,
    private router: Router,
    private modalService: NzModalService
  ) { }

  private updateLocalStorageValid(): void {
    this.isLocalStorageValid = localStorage.getItem('userKl');
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> {
    const token = this.auth.getToken();
    const user = this.auth.getUser();
    const validLogin = this.auth.isValidLogin;
    if (validLogin) {
      return of(true);
    } else {
      // If no token or user, check userProfile from localStorage
      return interval(500).pipe(
        take(1),
        switchMap(() => {
          const userProfileStr = localStorage.getItem('userKl');
          let userProfile: { name?: string; email?: string } | null = null;
          if (userProfileStr) {
            try {
              userProfile = JSON.parse(userProfileStr);
            } catch (e) {
              userProfile = null;
            }
          }
          if (userProfile && userProfile.name && userProfile.email) {
            return of(true);
          } else {
            localStorage.removeItem('permissions');
            return this.openLoginPrompt();
          }
        })
      );
    }
  }

  private openLoginPrompt(): Observable<boolean> {
    return new Observable<boolean>(observer => {
      const modal = this.modalService.warning({
        nzTitle: '<b>Login required</b>',
        nzContent: 'Your session has expired or you are not logged in. Please log in again to continue.',
        nzClassName: 'custom-login-modal-required',
        nzOnOk: () => {
          this.router.navigate(['/login']);
          modal.destroy();
          observer.next(false);
          observer.complete();
        },
        nzWidth: '450px',
        nzClosable: false,
        nzMaskClosable: false
      });
    });
  }
}
