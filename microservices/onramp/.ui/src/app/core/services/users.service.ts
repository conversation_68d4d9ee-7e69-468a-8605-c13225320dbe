import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { delay } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class UsersService {
  constructor(private http: HttpClient) { }
  getRoleUsers(organizationId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${organizationId}/roles`).pipe(delay(500));
  }
  getInvitations(userId: any): Observable<any[]> {
    return this.http.get<any[]>(`/api/organizations/${userId}/invites`).pipe(delay(500));
  }

  createInvaitions(organizationId: any, data: any): Observable<any[]> {
    return this.http.post<any[]>(`/api/organizations/${organizationId}/invites`, data);
  }
}
