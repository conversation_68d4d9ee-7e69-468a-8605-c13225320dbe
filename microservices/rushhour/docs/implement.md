### Rush Hour Refactor — Step-by-Step Implementation Plan

This document provides a concrete, testable plan to refactor the Rush Hour (RH) microservice from a POC to a production-grade service, aligned with the Synapse ITS Go Microservices Coding Rules and Style Guide.

---

### Objectives

- Productionize Socket.IO service with Redis-backed horizontal scaling.
- Enforce standardized authentication and authorization flows for FSA and Gateway clients using shared `authorizer` and DB.
- Adopt direct socket messaging for device commands and room-based broadcasting for streaming, with robust recovery.
- Align with dependency injection, graceful shutdown, logging, and testing conventions.
- Establish clear observability, rollout, and acceptance criteria.

---

### Scope

- In-scope: `microservices/rushhour` only, including `app`, `modules/socketio`, `auth`, `domain`, `permissions`, `tracking`, and docs.
- Out-of-scope: Gateway firmware/protocol changes; Onramp app code; unrelated microservices.

---

### Target Architecture Summary

- Namespaces: `/auth/fsa` and `/auth/gateway` with separate auth handlers and post-auth events.
- Messaging:
  - Commands and responses: direct socket messaging using session/socket IDs.
  - Streaming: room-based broadcasting per device and stream type.
- Scaling: Socket.IO Redis adapter for rooms; Redis KV for session, gateway, and viewer tracking data.
- Persistence: Device↔Gateway mapping resolved via Postgres through `shared/connect` with Redis caching.
- Observability: structured logs, adapter room lifecycle logs, healthz, and basic stats endpoints.

---

### Relevant Files

- `microservices/rushhour/main.go` — Process orchestration, DI, healthz, graceful shutdown.
- `microservices/rushhour/app/app.go` — App wiring, Socket.IO server injection, router setup.
- `microservices/rushhour/app/routes.go` — Global middleware, CORS, router.
- `microservices/rushhour/modules/socketio/service.go` — Core service, Redis adapter, session and gateway registry, device routing, stream control.
- `microservices/rushhour/auth/fsa.go` — FSA JWT validation using shared `authorizer` and context registration.
- `microservices/rushhour/auth/gateway.go` — Gateway MachineKey/APIKey validation and registration/recovery.
- `microservices/rushhour/domain/envelope.go` — Protobuf envelope re-exports and constants.
- `microservices/rushhour/domain/socket_registry.go` — Local socket registry for single-instance fallback.
- `microservices/rushhour/permissions/checker.go` — Permission checks for device and room access.
- `microservices/rushhour/tracking/stream_tracker.go` — Viewer tracking (Redis-backed with local fallback).

---

### Step-by-Step Implementation

1) Baseline, branch, and environment
- Create a feature branch for the refactor.
- Ensure environment variables are set:
  - `HEALTH_PORT` (e.g., ":8081")
  - `RH_REDIS` (e.g., "redis:6379").
  - `LOG_LEVEL` (e.g., "debug").
- Verify `go mod tidy` and `go test ./...` are green under `microservices/rushhour`.

2) Normalize dependency injection and process lifecycle
- Keep `main.go` minimal; delegate to `Run(...)` per project pattern.
- Ensure `Run(...)` wires `healthz`, connections, batcher, HTTP server, and graceful shutdown.
- Confirm DI entry points: `connect.NewConnections`, `bqbatch.New`, `DefaultServer`, `DefaultSignalChan`.

3) Socket.IO server with Redis adapter
- Use `RH_REDIS` to build the Socket.IO server with Redis adapter in `createSocketIOServerWithRedis(...)`.
- Log adapter lifecycle events (`create-room`, `delete-room`, `join-room`, `leave-room`).
- Behavior without `RH_REDIS`: run with local adapter; log a warning.

4) Redis-backed cross-instance state
- Store per-connection context in Redis with TTL, keyed by `rushhour:session:<socketId>`.
- Register connected gateways: `rushhour:gateway:<gatewayId>:socket` with TTL and cleanup on disconnect.
- Cache device→gateway mapping: `rushhour:device:<deviceId>:gateway` with an appropriate TTL.

5) Authentication flows and namespaces
- FSA (`/auth/fsa`):
  - JWT in `.auth.token`.
  - Validate via shared `authorizer.ValidateJWTAndGetPermissions`.
  - On success: store `ConnectionContext` and emit `auth_success` with `user_id` and `expires_at`.
- Gateway (`/auth/gateway`):
  - Machine key and API key in `.auth.machine_key` and `.auth.api_key`.
  - Validate via shared `authorizer.ValidateGatewayAuth`.
  - On success: store `ConnectionContext`, register gateway in Redis, attempt stream recovery, emit `gateway_init`.
- Ensure auth errors emit explicit `error` events with actionable messages.

6) Direct socket messaging for device commands
- FSA/Onramp → Gateway: `device_request` received on FSA namespace.
  - Validate device access via `permissions.PermissionChecker`.
  - Enrich envelope: `SessionId`, `UserId`, `OrganizationId`, `Origin`.
  - Resolve gateway socket via Redis/DB; emit to gateway namespace room for that socket.
- Gateway → FSA/Onramp: `device_message` received on Gateway namespace.
  - If `SessionId` present: route back to the FSA/Onramp socket with `device_message`.
  - If no `SessionId`: treat as streaming data; broadcast to active device stream rooms.

7) Streaming, rooms, and viewer tracking
- Rooms per device and stream type: `org:<org_id>:device:<device_id>:<stream_type>` where `<stream_type>` is `stream_display` or `stream_rms`.
- On FSA/Onramp join:
  - Validate permissions; track viewer in `StreamTracker` using composite key `deviceID:streamType`.
  - Join the room; if first viewer, send `stream_control` to gateway with `join_device_room`.
- On leave or disconnect:
  - Remove viewer; if last viewer, send `stream_control` with `leave_device_room`.
- On gateway reconnect:
  - Query DB for gateway org and devices; check Redis for active rooms; send recovery `stream_control` joins.

8) Permissions model
- Gateways: implicit access to their managed devices (validated by device↔gateway DB relation).
- FSA/Onramp: require any of view/manage permissions consistent with broker (`org_view_devices`, `device_group_view_devices`, etc.).
- Future: extend to command-level permissions once command taxonomy is finalized.

9) Error handling and logging
- Define package-level error variables where applicable; wrap with `%w`.
- Structured logs for major operations with IDs and context.
- Log Redis/DB errors at `Warn` when fallback applies; `Error` when fatal.

10) Observability and health
- Health endpoints via `shared/healthz`: `/startupz`, `/readyz`, `/livez` (health port).
- Socket.IO adapter room lifecycle logs are enabled.
- Add service stats endpoint by exposing `Service.GetStats()` via a future `/stats` HTTP handler if needed.

11) Backward compatibility and API stability
- Maintain `/socket.io/` HTTP route.
- Maintain `auth_success` for FSA and `gateway_init` for Gateway post-auth events.
- Keep binary-only envelope transport for performance.

12) Testing strategy
- Unit tests: auth handlers, permission checks, stream tracker, connection registry, gateway recovery, device routing.
- Integration tests (Redis present): multi-instance semantics for sessions, rooms, and recovery.
- E2E happy paths:
  - FSA auth → device_request → gateway device_message (with SessionId).
  - Streaming: join/leave with first/last viewer triggers.
  - Gateway reconnect triggers recovery.
- Negative paths:
  - Missing/invalid auth, unauthorized device access, disconnected gateway routing.
- Deterministic transport for tests: polling-only in test clients.

13) Rollout plan
- Staging deploy with Redis adapter enabled; validate E2E flows.
- Canary in production: small subset of gateways and FSAs.
- Observe logs and metrics; expand gradually.

---

### Implementation Checklist (Suggested PR Breakdown)

- [ ] PR1: Docs and scaffolding
  - Add this `implement.md` and ensure README/docs reference.
  - Verify `main.Run(...)` wiring matches conventions.
- [ ] PR2: Socket.IO Redis adapter and server events
  - Finalize `createSocketIOServerWithRedis(...)`; ensure warnings without `RH_REDIS`.
  - Enable adapter room lifecycle logs.
- [ ] PR3: Redis-backed session and gateway registry
  - Implement session storage and gateway socket registration; TTLs and cleanup.
  - Add `getGatewaySocketID(...)` with retry/backoff.
- [ ] PR4: Auth paths
  - FSA: JWT validation via shared authorizer; emit `auth_success`.
  - Gateway: MachineKey+APIKey validation; emit `gateway_init` and register gateway.
- [ ] PR5: Direct messaging flows
  - FSA `device_request` enrichment and routing to gateway.
  - Gateway `device_message` routing back by `SessionId`; streaming broadcast path when no `SessionId`.
- [ ] PR6: Streaming join/leave + viewer tracking
  - Room naming; permission checks; first/last viewer start/stop commands.
  - Disconnect cleanup and gateway recovery.
- [ ] PR7: Permissions hardening and tests
  - Align required permissions with broker; add unit tests.
- [ ] PR8: Observability and stability
  - Health readiness transitions; additional stats if needed.
  - Audit logs; elevate critical failures.
- [ ] PR9: E2E integration tests
  - Deterministic polling-only clients; full happy/negative paths.

---

### Environment Variables

- `HEALTH_PORT`: Port for health endpoints (e.g., ":8081").
- `RH_REDIS`: Redis address for adapter and trackers (e.g., "redis:6379").
- `LOG_LEVEL`: Logging level (debug, info, warn, error).

---

### Acceptance Criteria

- Auth flows:
  - FSA receives `auth_success` with valid JWT.
  - Gateway receives `gateway_init` with valid MachineKey+APIKey.
- Command routing:
  - `device_request` from FSA reaches the correct gateway.
  - `device_message` from gateway returns to the originating session when `SessionId` is present.
- Streaming:
  - First join triggers `join_device_room`; last leave triggers `leave_device_room` per stream type.
  - Gateway reconnect triggers recovery for active streams.
- Scaling:
  - Multiple RH instances share rooms and state via Redis; sessions and gateway mappings persist across instances.
- Resilience:
  - Graceful shutdown, health transitions, and no data loss in normal stop.
- Tests:
  - Unit, integration, and E2E tests pass in CI with polling-only transport for deterministic timing.

---

### Risks and Mitigations

- Redis availability: run with fallback locally; degrade gracefully; surface warnings.
- Stale gateway mappings: use TTLs and cleanup on disconnect; retry with backoff on lookup.
- Permission drift: keep broker-aligned permission names; centralize via shared `authorizer`.
- Client auth misuse: document `.auth` object usage; emit explicit error events and add integration tests.

---

### Notes

- Keep code readable with explicit, descriptive names; follow DI and interface patterns for testability.
- Use structured logging with identifiers (socket ID, user ID, org ID, device ID, gateway ID).
- Prefer binary transport and protobuf envelopes for performance; decode only when needed for filtering.


