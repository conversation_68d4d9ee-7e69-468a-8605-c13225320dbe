package rest

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	config "synapse-its.com/shared/rest/onramp/softwaregateway/config"
	"synapse-its.com/testing/utils"
)

// Test data constants from dev.sql
var (
	// Test Organization IDs from dev.sql
	testOrg1ID     = uuid.MustParse("550e8400-e29b-41d4-a716-************") // Test Organization 1
	testOrg2ID     = uuid.MustParse("550e8400-e29b-41d4-a716-************") // Test Organization 2
	corneliusOrgID = uuid.MustParse("c469a554-f7a8-5de5-a57e-e1ba16f970d3") // Synapse-Plano-Demo (has software gateways)

	// Software Gateway IDs from dev.sql
	testSoftwareGatewayID  = uuid.MustParse("550e8400-e29b-41d4-a716-************") // Test Gateway 1 in testOrg1ID
	testSoftwareGateway2ID = uuid.MustParse("550e8400-e29b-41d4-a716-************") // Test Gateway 2 in testOrg2ID

	// Test user credentials from dev.sql
	adminUserEmail    = "<EMAIL>"
	adminUserPassword = "puppies1234" // Hash in dev.sql corresponds to this

	// Base URL for onramp service
	baseURL = "http://onramp:8080/api"
)

// Helper structures for API responses
type APIResponse struct {
	Code    int         `json:"code"`
	Data    interface{} `json:"data"`
	Message string      `json:"message"`
	Status  string      `json:"status"`
}

// setupIntegrationTest sets up the test environment and returns an authenticated HTTP client
func setupIntegrationTest(t *testing.T) (*http.Client, *http.Cookie) {
	t.Helper()

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Wait for onramp service to be ready
	require.NoError(t, utils.AwaitOnramp(ctx, 5*time.Second), "onramp service should be ready")

	// Perform login to get session cookie
	sessionCookie := utils.PerformBasicAuthLogin(t, adminUserEmail, adminUserPassword)
	require.NotNil(t, sessionCookie, "Should have received a session cookie")

	// Create HTTP client with timeout
	client := &http.Client{Timeout: 10 * time.Second}

	return client, sessionCookie
}

// makeAuthenticatedRequest makes an HTTP request with the session cookie
func makeAuthenticatedRequest(t *testing.T, client *http.Client, sessionCookie *http.Cookie, method, url string, body interface{}) *http.Response {
	t.Helper()

	var bodyReader io.Reader
	if body != nil {
		bodyBytes, err := json.Marshal(body)
		require.NoError(t, err, "Should be able to marshal request body")
		bodyReader = bytes.NewReader(bodyBytes)
	}

	req, err := http.NewRequest(method, url, bodyReader)
	require.NoError(t, err, "Should be able to create request")

	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}
	req.AddCookie(sessionCookie)

	resp, err := client.Do(req)
	require.NoError(t, err, "Should be able to make request")

	return resp
}

// parseAPIResponse parses an API response into the standard format
func parseAPIResponse(t *testing.T, resp *http.Response, target interface{}) *APIResponse {
	t.Helper()

	body, err := io.ReadAll(resp.Body)
	require.NoError(t, err, "Should be able to read response body")
	defer resp.Body.Close()

	var apiResp APIResponse
	err = json.Unmarshal(body, &apiResp)
	require.NoError(t, err, "Should be able to parse JSON response")

	if target != nil && apiResp.Data != nil {
		// Re-marshal and unmarshal to convert interface{} to target type
		dataBytes, err := json.Marshal(apiResp.Data)
		require.NoError(t, err, "Should be able to marshal data")
		err = json.Unmarshal(dataBytes, target)
		require.NoError(t, err, "Should be able to unmarshal data to target type")
	}

	return &apiResp
}

// Test Template Management Endpoints
func TestSoftwareGatewayConfig_TemplateManagement_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	t.Run("Create Template", func(t *testing.T) {
		// Create a new template
		createReq := config.CreateGatewayConfigTemplateRequest{
			Name:        "Test Integration Template",
			Description: "Template created by integration test",
		}

		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates", baseURL, testOrg1ID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		var template config.GatewayConfigTemplate
		apiResp := parseAPIResponse(t, resp, &template)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, createReq.Name, template.Name)
		assert.Equal(t, createReq.Description, template.Description)
		assert.Equal(t, testOrg1ID, template.OrganizationId)
		assert.NotEmpty(t, template.Id)

		// Store template ID for later tests
		templateID := template.Id

		t.Run("Get Template", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var retrievedTemplate config.GatewayConfigTemplate
			apiResp := parseAPIResponse(t, resp, &retrievedTemplate)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Equal(t, template.Id, retrievedTemplate.Id)
			assert.Equal(t, template.Name, retrievedTemplate.Name)
		})

		t.Run("Update Template", func(t *testing.T) {
			updateReq := config.UpdateGatewayConfigTemplateRequest{
				Name:        "Updated Integration Template",
				Description: "Updated by integration test",
			}

			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, updateReq)

			var updatedTemplate config.GatewayConfigTemplate
			apiResp := parseAPIResponse(t, resp, &updatedTemplate)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Equal(t, updateReq.Name, updatedTemplate.Name)
			assert.Equal(t, updateReq.Description, updatedTemplate.Description)
		})

		t.Run("List Templates", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates", baseURL, testOrg1ID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var templates []config.GatewayConfigTemplate
			apiResp := parseAPIResponse(t, resp, &templates)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.GreaterOrEqual(t, len(templates), 1, "Should have at least the template we created")

			// Find our template
			found := false
			for _, tmpl := range templates {
				if tmpl.Id == templateID {
					found = true
					assert.Equal(t, "Updated Integration Template", tmpl.Name)
					break
				}
			}
			assert.True(t, found, "Should find our created template in the list")
		})

		t.Run("Delete Template", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, url, nil)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify template is deleted by trying to get it (should return 404)
			getResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
			assert.Equal(t, http.StatusNotFound, getResp.StatusCode)
		})
	})
}

// Test Template Settings Management
func TestSoftwareGatewayConfig_TemplateSettings_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	// Get an existing template from the database (created by dev.sql)
	ctx := context.Background()
	connections := connect.NewConnections(ctx)
	defer connections.Close()

	// Query for a template in testOrg1
	query := `SELECT Id FROM {{GatewayConfigTemplate}} WHERE OrganizationId = $1 LIMIT 1`
	var templateResult struct {
		Id uuid.UUID `db:"id"`
	}
	err := connections.Postgres.QueryRowStruct(&templateResult, query, testOrg1ID)
	require.NoError(t, err, "Should find an existing template")
	templateID := templateResult.Id

	t.Run("Template Settings CRUD", func(t *testing.T) {
		// Create a setting
		createReq := config.CreateOrUpdateGatewayConfigTemplateSettingRequest{
			GatewayConfigTemplateId: templateID,
			Setting:                 "test_integration_setting",
			Value:                   "test_value",
		}

		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings", baseURL, testOrg1ID, templateID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		var setting config.GatewayConfigTemplateSetting
		apiResp := parseAPIResponse(t, resp, &setting)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, createReq.Setting, setting.Setting)
		assert.Equal(t, createReq.Value, setting.Value)

		t.Run("Get Template Settings", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var settings []config.GatewayConfigTemplateSetting
			apiResp := parseAPIResponse(t, resp, &settings)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Greater(t, len(settings), 0, "Should have settings")

			// Find our test setting
			found := false
			for _, s := range settings {
				if s.Setting == "test_integration_setting" {
					found = true
					assert.Equal(t, "test_value", s.Value)
					break
				}
			}
			assert.True(t, found, "Should find our test setting")
		})

		t.Run("Bulk Replace Template Settings", func(t *testing.T) {
			bulkSettings := []config.GatewayConfigTemplateSetting{
				{
					GatewayConfigTemplateId: templateID,
					Setting:                 "bulk_setting_1",
					Value:                   "bulk_value_1",
				},
				{
					GatewayConfigTemplateId: templateID,
					Setting:                 "bulk_setting_2",
					Value:                   "bulk_value_2",
				},
			}

			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPut, url, bulkSettings)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify the settings were replaced - with retry for race conditions
			var settings []config.GatewayConfigTemplateSetting
			var getResp *http.Response
			settingMap := make(map[string]string)
			foundCorrectSettings := false

			// Retry up to 5 times with increasing delays
			for attempt := 0; attempt < 5; attempt++ {
				if attempt > 0 {
					time.Sleep(time.Duration(attempt*100) * time.Millisecond) // 100ms, 200ms, 300ms, 400ms
				}

				getResp = makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
				settings = nil                       // Reset slice
				settingMap = make(map[string]string) // Reset map
				parseAPIResponse(t, getResp, &settings)

				// Check if we have the expected 2 settings
				if len(settings) == 2 {
					for _, s := range settings {
						settingMap[s.Setting] = s.Value
					}

					// Check if we have the correct settings
					if settingMap["bulk_setting_1"] == "bulk_value_1" && settingMap["bulk_setting_2"] == "bulk_value_2" {
						foundCorrectSettings = true
						break
					}
				}
			}

			// Should only have the 2 bulk settings now (previous settings replaced)
			assert.Len(t, settings, 2)
			assert.True(t, foundCorrectSettings, "Should find the correct bulk settings after retries")
			assert.Equal(t, "bulk_value_1", settingMap["bulk_setting_1"])
			assert.Equal(t, "bulk_value_2", settingMap["bulk_setting_2"])
		})

		t.Run("Delete Template Setting", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings/bulk_setting_1", baseURL, testOrg1ID, templateID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, url, nil)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify setting is deleted
			getUrl := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s/settings", baseURL, testOrg1ID, templateID)
			getResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, getUrl, nil)
			var settings []config.GatewayConfigTemplateSetting
			parseAPIResponse(t, getResp, &settings)

			for _, s := range settings {
				assert.NotEqual(t, "bulk_setting_1", s.Setting, "Deleted setting should not be present")
			}
		})
	})
}

// Test Override Management (including new bulk upsert)
func TestSoftwareGatewayConfig_Overrides_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	// Use the existing software gateway from dev.sql
	softwareGatewayID := testSoftwareGatewayID
	orgID := testOrg1ID // Test Gateway 1 belongs to Test Organization 1

	t.Run("Override CRUD Operations", func(t *testing.T) {
		// Create an override (base setting will be created automatically by the handler)
		createReq := config.CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
			SoftwareGatewayId: softwareGatewayID,
			Setting:           "test_override_setting",
			Value:             "override_value",
		}

		url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, orgID, softwareGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		var override config.GatewayConfigTemplateSettingOverride
		apiResp := parseAPIResponse(t, resp, &override)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, createReq.Setting, override.Setting)
		assert.Equal(t, createReq.Value, override.Value)
		assert.Equal(t, softwareGatewayID, override.SoftwareGatewayId)

		t.Run("Get Overrides", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, orgID, softwareGatewayID)

			// Retry mechanism to handle potential race conditions
			var overrides []config.GatewayConfigTemplateSettingOverride
			var apiResp *APIResponse
			var resp *http.Response
			found := false

			// Retry up to 5 times with increasing delays
			for attempt := 0; attempt < 5; attempt++ {
				if attempt > 0 {
					time.Sleep(time.Duration(attempt*100) * time.Millisecond) // 100ms, 200ms, 300ms, 400ms
				}

				resp = makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
				apiResp = parseAPIResponse(t, resp, &overrides)

				if resp.StatusCode == http.StatusOK && apiResp.Status == "success" && len(overrides) > 0 {
					// Find our test override
					for _, o := range overrides {
						if o.Setting == "test_override_setting" {
							found = true
							assert.Equal(t, "override_value", o.Value)
							break
						}
					}

					if found {
						break // Success - exit retry loop
					}
				}
			}

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Greater(t, len(overrides), 0, "Should have overrides")
			assert.True(t, found, "Should find our test override")
		})

		t.Run("Bulk Upsert Overrides", func(t *testing.T) {
			// Base settings will be created automatically by the handler
			bulkRequest := config.BulkUpsertGatewayConfigTemplateSettingOverridesRequest{
				"upsert_setting_1":      "upsert_value_1",
				"upsert_setting_2":      "upsert_value_2",
				"test_override_setting": "updated_override_value", // This should update the existing one
			}

			url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides/bulk", baseURL, orgID, softwareGatewayID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, bulkRequest)

			var overrides []config.GatewayConfigTemplateSettingOverride
			apiResp := parseAPIResponse(t, resp, &overrides)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Len(t, overrides, 3, "Should return 3 overrides")

			// Verify all settings are present with correct values
			overrideMap := make(map[string]string)
			for _, o := range overrides {
				overrideMap[o.Setting] = o.Value
				assert.Equal(t, softwareGatewayID, o.SoftwareGatewayId)
			}
			assert.Equal(t, "upsert_value_1", overrideMap["upsert_setting_1"])
			assert.Equal(t, "upsert_value_2", overrideMap["upsert_setting_2"])
			assert.Equal(t, "updated_override_value", overrideMap["test_override_setting"])
		})

		t.Run("Delete Override", func(t *testing.T) {
			url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides/upsert_setting_1", baseURL, orgID, softwareGatewayID)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, url, nil)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify override is deleted
			getUrl := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, orgID, softwareGatewayID)
			getResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, getUrl, nil)
			var overrides []config.GatewayConfigTemplateSettingOverride
			parseAPIResponse(t, getResp, &overrides)

			for _, o := range overrides {
				assert.NotEqual(t, "upsert_setting_1", o.Setting, "Deleted override should not be present")
			}
		})
	})
}

// Test Legacy Bulk Upsert Endpoint (without organizationId in URL)
func TestSoftwareGatewayConfig_LegacyBulkUpsert_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	// Use the existing software gateway from dev.sql
	softwareGatewayID := testSoftwareGatewayID

	t.Run("Legacy Bulk Upsert Without OrganizationId", func(t *testing.T) {
		// Base settings will be created automatically by the handler
		bulkRequest := config.BulkUpsertGatewayConfigTemplateSettingOverridesRequest{
			"legacy_setting_1": "legacy_value_1",
			"legacy_setting_2": "legacy_value_2",
		}

		// Note: URL does not include organizationId - this is the legacy endpoint
		url := fmt.Sprintf("%s/softwaregateway/%s/config", baseURL, softwareGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, bulkRequest)

		var overrides []config.GatewayConfigTemplateSettingOverride
		apiResp := parseAPIResponse(t, resp, &overrides)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Len(t, overrides, 2, "Should return 2 overrides")

		// Verify settings are correct
		overrideMap := make(map[string]string)
		for _, o := range overrides {
			overrideMap[o.Setting] = o.Value
			assert.Equal(t, softwareGatewayID, o.SoftwareGatewayId)
		}
		assert.Equal(t, "legacy_value_1", overrideMap["legacy_setting_1"])
		assert.Equal(t, "legacy_value_2", overrideMap["legacy_setting_2"])

		logger.Infof("Legacy bulk upsert successful for gateway %s", softwareGatewayID)
	})

	t.Run("Legacy Endpoint Error Cases", func(t *testing.T) {
		// Test with invalid gateway ID
		invalidGatewayID := uuid.New()
		bulkRequest := config.BulkUpsertGatewayConfigTemplateSettingOverridesRequest{
			"setting": "value",
		}

		url := fmt.Sprintf("%s/softwaregateway/%s/config", baseURL, invalidGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, bulkRequest)

		assert.Equal(t, http.StatusNotFound, resp.StatusCode, "Should return 404 for non-existent gateway")

		// Test with empty settings
		emptyRequest := config.BulkUpsertGatewayConfigTemplateSettingOverridesRequest{}
		url = fmt.Sprintf("%s/softwaregateway/%s/config", baseURL, softwareGatewayID)
		resp = makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, emptyRequest)

		assert.Equal(t, http.StatusBadRequest, resp.StatusCode, "Should return 400 for empty settings")
	})
}

// Test Base Settings Management
func TestSoftwareGatewayConfig_BaseSettings_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	t.Run("Base Settings CRUD", func(t *testing.T) {
		// Create a base setting
		baseSetting := config.GatewayConfigTemplateBaseSetting{
			Setting:      "test_integration_base_setting",
			DefaultValue: "default_test_value",
			Name:         "Test Integration Base Setting",
			Description:  "Base setting created by integration test",
			Format:       `{"type":"string"}`,
		}

		url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings", baseURL)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, baseSetting)

		apiResp := parseAPIResponse(t, resp, nil)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)

		t.Run("Get Base Setting", func(t *testing.T) {
			url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings/%s", baseURL, baseSetting.Setting)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var retrievedSetting config.GatewayConfigTemplateBaseSetting
			apiResp := parseAPIResponse(t, resp, &retrievedSetting)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Equal(t, baseSetting.Setting, retrievedSetting.Setting)
			assert.Equal(t, baseSetting.Name, retrievedSetting.Name)
			assert.Equal(t, baseSetting.DefaultValue, retrievedSetting.DefaultValue)
		})

		t.Run("Update Base Setting", func(t *testing.T) {
			updatedSetting := baseSetting
			updatedSetting.DefaultValue = "updated_default_value"
			updatedSetting.Name = "Updated Test Base Setting"

			url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings/%s", baseURL, baseSetting.Setting)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPatch, url, updatedSetting)

			var result config.GatewayConfigTemplateBaseSetting
			apiResp := parseAPIResponse(t, resp, &result)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Equal(t, updatedSetting.DefaultValue, result.DefaultValue)
			assert.Equal(t, updatedSetting.Name, result.Name)
		})

		t.Run("List Base Settings", func(t *testing.T) {
			url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings", baseURL)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

			var settings []config.GatewayConfigTemplateBaseSetting
			apiResp := parseAPIResponse(t, resp, &settings)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)
			assert.Greater(t, len(settings), 0, "Should have base settings")

			// Find our test setting
			found := false
			for _, s := range settings {
				if s.Setting == baseSetting.Setting {
					found = true
					assert.Equal(t, "updated_default_value", s.DefaultValue)
					break
				}
			}
			assert.True(t, found, "Should find our test base setting")
		})

		t.Run("Delete Base Setting", func(t *testing.T) {
			url := fmt.Sprintf("%s/softwaregatewayconfig/basesettings/%s", baseURL, baseSetting.Setting)
			resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodDelete, url, nil)

			apiResp := parseAPIResponse(t, resp, nil)

			assert.Equal(t, http.StatusOK, resp.StatusCode)
			assert.Equal(t, "success", apiResp.Status)

			// Verify setting is deleted
			getResp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)
			assert.Equal(t, http.StatusNotFound, getResp.StatusCode)
		})
	})
}

// Test Error Scenarios and Permission Handling
func TestSoftwareGatewayConfig_ErrorScenarios_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	t.Run("Invalid Organization ID", func(t *testing.T) {
		invalidOrgID := uuid.New()
		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates", baseURL, invalidOrgID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

		// Should return empty list for non-existent organization
		var templates []config.GatewayConfigTemplate
		apiResp := parseAPIResponse(t, resp, &templates)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Empty(t, templates, "Should return empty list for non-existent organization")
	})

	t.Run("Invalid Template ID", func(t *testing.T) {
		invalidTemplateID := uuid.New()
		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates/%s", baseURL, testOrg1ID, invalidTemplateID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})

	t.Run("Invalid Software Gateway ID", func(t *testing.T) {
		invalidGatewayID := uuid.New()
		url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, testOrg1ID, invalidGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodGet, url, nil)

		assert.Equal(t, http.StatusNotFound, resp.StatusCode)
	})

	t.Run("Malformed JSON", func(t *testing.T) {
		url := fmt.Sprintf("%s/organization/%s/softwaregatewayconfig/templates", baseURL, testOrg1ID)

		req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader([]byte("{invalid json")))
		require.NoError(t, err)
		req.Header.Set("Content-Type", "application/json")
		req.AddCookie(sessionCookie)

		resp, err := client.Do(req)
		require.NoError(t, err)
		defer resp.Body.Close()

		assert.Equal(t, http.StatusBadRequest, resp.StatusCode)
	})
}

// Test Database Consistency and Relationships
func TestSoftwareGatewayConfig_DatabaseConsistency_Integration(t *testing.T) {
	t.Parallel()

	client, sessionCookie := setupIntegrationTest(t)

	// Test that creating overrides for non-existent base settings still works
	// (the system should allow any setting name, not just those in base settings)
	t.Run("Override with Custom Setting Name", func(t *testing.T) {
		customSettingName := fmt.Sprintf("custom_setting_%d", time.Now().Unix())

		createReq := config.CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
			SoftwareGatewayId: testSoftwareGatewayID,
			Setting:           customSettingName,
			Value:             "custom_value",
		}

		url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, testOrg1ID, testSoftwareGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		var override config.GatewayConfigTemplateSettingOverride
		apiResp := parseAPIResponse(t, resp, &override)

		assert.Equal(t, http.StatusOK, resp.StatusCode)
		assert.Equal(t, "success", apiResp.Status)
		assert.Equal(t, customSettingName, override.Setting)
		assert.Equal(t, "custom_value", override.Value)
	})

	// Test cascade behavior - this depends on your database constraints
	t.Run("Software Gateway Organization Consistency", func(t *testing.T) {
		// Try to create override for gateway in different organization
		// This should fail due to the verification logic in the handlers

		createReq := config.CreateOrUpdateGatewayConfigTemplateSettingOverrideRequest{
			SoftwareGatewayId: testSoftwareGatewayID, // This gateway belongs to testOrg1ID
			Setting:           "test_cross_org_setting",
			Value:             "should_fail",
		}

		// Try to create it under corneliusOrgID (wrong organization)
		url := fmt.Sprintf("%s/organization/%s/softwaregateway/%s/config/overrides", baseURL, corneliusOrgID, testSoftwareGatewayID)
		resp := makeAuthenticatedRequest(t, client, sessionCookie, http.MethodPost, url, createReq)

		assert.Equal(t, http.StatusNotFound, resp.StatusCode, "Should fail when gateway doesn't belong to organization")
	})
}
